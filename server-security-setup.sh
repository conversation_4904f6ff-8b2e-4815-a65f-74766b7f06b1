#!/bin/bash

# 服务器安全配置脚本
# 使用方法: sudo bash server-security-setup.sh YOUR_IP_ADDRESS

if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo运行此脚本"
    exit 1
fi

if [ -z "$1" ]; then
    echo "使用方法: sudo bash $0 YOUR_IP_ADDRESS"
    echo "例如: sudo bash $0 *************"
    exit 1
fi

YOUR_IP=$1
SSH_PORT=2222
USERNAME=$(logname)

echo "开始配置服务器安全设置..."
echo "您的IP地址: $YOUR_IP"
echo "SSH端口: $SSH_PORT"
echo "用户名: $USERNAME"

# 1. 更新系统
echo "更新系统包..."
apt update && apt upgrade -y

# 2. 安装必要软件
echo "安装安全软件..."
apt install -y ufw fail2ban

# 3. 配置SSH
echo "配置SSH安全设置..."
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.backup

cat > /etc/ssh/sshd_config << EOF
Port $SSH_PORT
PermitRootLogin no
PasswordAuthentication no
PubkeyAuthentication yes
MaxAuthTries 3
LoginGraceTime 60
AllowUsers $USERNAME
PermitEmptyPasswords no
X11Forwarding no
PrintMotd no
UsePAM yes
AcceptEnv LANG LC_*
Subsystem sftp /usr/lib/openssh/sftp-server
EOF

# 4. 配置防火墙
echo "配置防火墙..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing
ufw allow from $YOUR_IP to any port $SSH_PORT
ufw allow 80
ufw allow 443
ufw --force enable

# 5. 配置Fail2Ban
echo "配置Fail2Ban..."
cat > /etc/fail2ban/jail.local << EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
ignoreip = 127.0.0.1/8 $YOUR_IP

[sshd]
enabled = true
port = $SSH_PORT
filter = sshd
logpath = /var/log/auth.log
maxretry = 3
bantime = 3600
EOF

# 6. 重启服务
echo "重启服务..."
systemctl restart ssh
systemctl restart fail2ban
systemctl enable fail2ban

# 7. 显示配置信息
echo "================================"
echo "安全配置完成！"
echo "================================"
echo "SSH端口: $SSH_PORT"
echo "允许的IP: $YOUR_IP"
echo "用户名: $USERNAME"
echo ""
echo "重要提醒："
echo "1. 请确保您已经设置了SSH密钥认证"
echo "2. 下次登录请使用: ssh -p $SSH_PORT $USERNAME@服务器IP"
echo "3. 如果IP地址变化，请运行更新脚本"
echo ""
echo "查看防火墙状态: sudo ufw status"
echo "查看Fail2Ban状态: sudo fail2ban-client status"
echo "================================"
