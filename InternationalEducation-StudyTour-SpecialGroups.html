﻿<!-- ========== 新辉煌出国 InternationalEducation-StudyTour-SpecialGroups.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <!-- ========== 头部meta与样式引入区块 ========== -->
  <meta charset="utf-8" />
  <title>新辉煌出国</title>
  <meta content="width=device-width, initial-scale=1.0" name="viewport" />
  <meta content="" name="keywords" />
  <meta content="" name="description" />
  <link href="css/responsive.css" rel="stylesheet" />
  <!-- 本地图标字体，已本地化 -->
  <link rel="stylesheet" href="css/fontawesome/all.css" />
  <link rel="stylesheet" href="css/bootstrap-icons/bootstrap-icons.css" />

  <!-- 本地动画库、轮播库样式 -->
  <link href="lib/animate/animate.min.css" rel="stylesheet" />
  <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />

  <!-- 结构化区块样式 -->
  <link href="css/css4/gg1.css" rel="stylesheet" />
  <link rel="stylesheet" href="./css/merged/reset1.css" />
  <link rel="stylesheet" href="./css/merged/common1.css" />
  <link rel="stylesheet" href="./css/merged/window1.css" />
  <link rel="stylesheet" href="./css/merged/advisers1.css" />
  <link rel="stylesheet" href="./css/merged/flag_window.css" />
  <link rel="stylesheet" href="./css/merged/flag.css" />
  <link rel="stylesheet" href="./css/merged/swiper.css" />
  <link rel="stylesheet" href="./css/merged/globe_subject.css" />
  <link rel="stylesheet" href="./css/merged/public.css" />
  <link rel="stylesheet" href="./css/merged/index_new.css" />
  <link rel="stylesheet" href="./css/merged/project_page_cq_new.css" />
  <link rel="stylesheet" href="./css/merged/jinrErweima.css" />
  <link rel="stylesheet" href="./css/merged/flag(1).css" />

  <!-- 本地Bootstrap样式 -->
  <link href="css/bootstrap.min.css" rel="stylesheet" />

  <!-- 站点主样式，结构化注释 -->
  <link href="css/style.css" rel="stylesheet" />

  <link rel="shortcut icon" href="图片/新辉煌logo.png" />

  <style>
    /* 隐藏style2样式 */
    .style2 {
      display: none;
    }

    .hover:hover {
      color: var(--bs-secondary) !important;
    }
  </style>
  <!-- ========== 头部meta与样式引入区块 End ========== -->
</head>

<body>
  <!-- ========== 顶部加载动画区块 ========== -->
  <div id="spinner"
    class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
    <div class="spinner-border text-secondary" style="width: 109.5px; height: 109.5px" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <!-- ========== 顶部加载动画区块 End ========== -->

  <!-- 顶部栏区块 -->
  <div class="container-fluid bg-primary px-5 d-none d-lg-block">
    <div class="row gx-0 align-items-center">
      <div class="col-lg-5 text-center text-lg-start mb-lg-0">
        <div class="d-flex"></div>
      </div>
      <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
        <div class="d-inline-flex align-items-center" style="height: 45px"></div>
      </div>
      <div class="col-lg-4 text-center text-lg-end">
        <div class="d-inline-flex align-items-center" style="height: 45px"></div>
      </div>
    </div>
  </div>
  <!-- 顶部栏区块结束 -->

  <!-- ========== 导航栏区块 ========== -->
  <div class="container-fluid nav-bar p-0">
    <nav class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0">
      <a href="" class="navbar-brand p-0">
        <h1 class="display-5 text-secondary m-0">
          <picture>
            <source srcset="图片/logo.webp" type="image/webp">
            <img src="图片/logo.png" class="img-fluid" alt="" />
          </picture>
        </h1>
        <!-- <img src="img/logo.png" alt="Logo"> -->
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
        <span class="fa fa-bars"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarCollapse">
        <div class="navbar-nav ms-auto py-0">
          <a href="index.html" class="nav-item nav-link">首页</a>
          <a href="about.html" class="nav-item nav-link">关于我们</a>
          <div class="nav-item dropdown">
            <a href="Immigration.html" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='Immigration.html'">
              <span class="dropdown-toggle">移民项目</span>
            </a>
            <div class="dropdown-menu m-0">
              <a href=" Immigration-USA.html" class="dropdown-item">美国</a>
              <a href="Immigration-Canada.html" class="dropdown-item">加拿大</a>
              <a href="Immigration-Ireland.html" class="dropdown-item">爱尔兰</a>
              <a href="Immigration-HongKong.html" class="dropdown-item">香港</a>
              <a href="Immigration-SingaporeEP.html" class="dropdown-item">新加坡</a>
              <a href="Immigration-Grenada.html" class="dropdown-item">格林纳达</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link active" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#jiaoyu'"><span class="dropdown-toggle">国际教育</span></a>
            <div class="dropdown-menu m-0">
              <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item">留学申请</a>
              <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item">线上课程</a>
              <a href="InternationalEducation-TopSchools.html" class="dropdown-item">名校直通车计划</a>
              <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item">语言课程</a>
              <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item active">游学、特色团</a>
              <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item">国际学校</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#haiwai'"><span class="dropdown-toggle">海外置业</span></a>
            <div class="dropdown-menu m-0">
              <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
              <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
              <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
              <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#pingtai'"><span class="dropdown-toggle">平台合作</span></a>
            <div class="dropdown-menu m-0">
              <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item">合和法律平台</a>
              <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item">精英留学平台</a>
              <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item">新辉煌金融平台</a>
            </div>
          </div>
          <a href="contact.html" class="nav-item nav-link">联系我们</a>
        </div>
      </div>
    </nav>
  </div>
  <!-- ========== 导航栏区块 End ========== -->

   <!-- Header Start -->
    <div
      class="container-fluid bg-breadcrumb"
      style="
        background: linear-gradient(
            rgba(0, 58, 102, 0.9),
            rgba(0, 58, 102, 0.8)
          ),
          url(./img/breadcrumb.png);
        background-position: center center;
        background-repeat: no-repeat;
        background-attachment: initial;
        background-size: cover;
        padding: 100px 0 60px 0;
      "
    >
      <div class="container text-center py-5" style="max-width: 2000px">
        <h3
          class="text-white display-3 mb-4 wow fadeInDown"
          data-wow-delay="0.1s"
        >
          新辉煌国际教育名校合作特色游学项目<br />
        </h3>
        <h1 style="color: #ffffff">（Glorious EDU Summer School）</h1>
        <ol
          class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown"
          data-wow-delay="0.3s"
        >
          <li class="breadcrumb-item">
            <a href="index.html" class="text-white">主页</a>
          </li>

          <li class="breadcrumb-item active text-secondary">
            名校合作特色游学
          </li>
        </ol>
      </div>
    </div>
    <!-- Header End -->

  <!-- ========== 搜索弹窗区块 ========== -->
  <!-- Modal Search Start -->
  <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
      <div class="modal-content rounded-0">
        <div class="modal-header">
          <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">
            搜索
          </h4>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body d-flex align-items-center">
          <div class="input-group w-75 mx-auto d-flex">
            <input type="search" class="form-control p-3" placeholder="输入关键词搜索" aria-describedby="search-icon-1" />
            <span id="search-icon-1" class="input-group-text p-3"><i class="fa fa-search"></i></span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- Modal Search End -->
  <!-- ========== 搜索弹窗区块 End ========== -->

  <!-- ========== 主体内容区块 ========== -->
  <!-- Contact Start -->
  <div class="container-fluid contact overflow-hidden py-5" style="padding-bottom: 36.5px !important">
    <div class="container py-5" style="padding-bottom: 36.5px !important">
      <div class="row g-5 mb-5" style="justify-content: flex-end; margin-bottom: 0px !important">
        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 44%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">英国</h5>
            <h5>&nbsp;</h5>
          </div>
          <h1 class="display-5 mb-4">
            英国顶尖贵族私校&剑桥学区房 <br />7天6晚菁英考察团
          </h1>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>

          <div class="d-flex border-bottom mb-4 pb-4" style="
                align-items: center;
                border-bottom: 0rem solid #dee2e6 !important;
              ">
            <h5>时间:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              7天
            </p>
          </div>

          <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center">
            <h5>对象:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              以英国顶尖私校为目标的国际学生，7-17岁
            </p>
          </div>

          <h5>简介：</h5>
          <p class="mb-5">
            投资教育，赢在起点。【名校学位+以房养学】一次满足！
            行程由新辉煌国际教育与英国加比达斯教育集团、香港L&V海房国际联合倾心定制。<br />
            *
            加比达斯教育集团作为英国UKISET考试局官方、拥有140+年为英国及欧洲王室服务的历史，将陪同您一起探访英国顶尖贵族私校，揭开“精英教育”的神秘面纱；<br />
            *
            香港L&V海房国际，携原CBRE（世邦魏理仕）团队成员，为您带来剑桥学区房的全新投资机遇，与霍金、牛顿为邻，抢占顶尖名校入学推荐名额。
          </p>

          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align: center">
          <picture>
            <source srcset="图片/7天6晚菁英考察团.webp" type="image/webp">
            <img src="图片/7天6晚菁英考察团.png" class="img-fluid" alt="" style="max-width: 80%; margin: 0% 0 0 0%" />
          </picture>
        </div>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 44%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">美国</h5>
            <h5>&nbsp;</h5>
          </div>
          <h1 class="display-5 mb-4">
            美国加州大学欧文分校<br />
            暑期科研课程<br />
          </h1>
          <h3>(简称，UCI的EUR项目)</h3>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>

          <div class="d-flex border-bottom mb-4 pb-4" style="
                align-items: center;
                border-bottom: 0rem solid #dee2e6 !important;
              ">
            <h5>时间:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              2-6周
            </p>
          </div>

          <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center">
            <h5>对象:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              18周岁以上国际学生及美国本地学生
            </p>
          </div>

          <h5>简介：</h5>
          <p class="mb-5">
            在全美排名Top10的公立大学内上课、住宿，知名专家学者授课、小班教学、国际化校友环境，
            <b>完成课程后可获结业证书和UCI正式成绩单，并豁免大学学分。</b>
          </p>

          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align: center">
          <picture>
            <source srcset="图片/国际教育-游学、特色团1.webp" type="image/webp">
            <img src="图片/国际教育-游学、特色团1.png" class="img-fluid" alt="" style="max-width: 80%; margin: 0% 0 0 0%" />
          </picture>
        </div>

        <h5>
          &nbsp;<br />
          &nbsp;
        </h5>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 44%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">美国</h5>
            <h5>&nbsp;</h5>
          </div>
          <h1 class="display-5 mb-4">美国常春藤名校研学夏令营<br /></h1>
          <h3>(Harvard Program)</h3>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>

          <div class="d-flex border-bottom mb-4 pb-4" style="
                align-items: center;
                border-bottom: 0rem solid #dee2e6 !important;
              ">
            <h5>时间:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              11天
            </p>
          </div>

          <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center">
            <h5>对象:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              10周岁以上，可亲子同游，持美国签证者
            </p>
          </div>

          <h5>简介：</h5>
          <p class="mb-5">
            一次性探访哈耶普宾麻等7大常春藤名校，<b>特别安排哈佛大学专日深度体验课，</b>
            可获得哈佛纪念证书。同时走进美国政治、经济、金融中心，体验美国风土人情，探寻世界前沿教育模式。
          </p>
          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align: center">
          <picture>
            <source srcset="图片/国际教育-游学、特色团2.webp" type="image/webp">
            <img src="图片/国际教育-游学、特色团2.png" class="img-fluid" alt="" style="max-width: 80%; margin: 0% 0 0 0%" />
          </picture>
        </div>

        <h5>
          &nbsp;<br />
          &nbsp;
        </h5>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 45%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">美国</h5>
            <h5>&nbsp;</h5>
          </div>
          <h1 class="display-5 mb-4">
            美国SpaceX<br />
            太空探索大课堂
          </h1>
          <h3>(HASSE)</h3>
          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>
          <div class="d-flex border-bottom mb-4 pb-4" style="
                align-items: center;
                border-bottom: 0rem solid #dee2e6 !important;
              ">
            <h5>时间:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              11天
            </p>
          </div>

          <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center">
            <h5>对象:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              高中、大学学生，持美国签证者
            </p>
          </div>

          <h5>简介：</h5>
          <p class="mb-5">
            权威营地课程，全方面浸润太空知识，与SpaceX太空发射基地、
            美国太空总署约翰逊航天中心（NASA
            JSC）、休斯顿大学太空研究所、莱斯大学、
            休斯顿太空与科学教育协会（HASSE）顶级机构合作，为准备读世界名校本科和研究生的孩子专门定制的高阶课程。
            这是一个国际罕见而高端的太空教育大课堂，将创业家精神和太空科学相结合，以太空教育为主轴，
            以 STEM课程设计为理念，通过情景模拟的融入式体验式学习，让学生实地操作，培养动手能力和解决问题的能力，
            引导学生像设计师一样团队讨论、计划，也像工程师一样，动手实践(hands-on)、解读数据、制作模型与实测，最后发表团队成果。
            提供给学生的是独一无二的最极致的STEM学习体验。课程结束可获得美国休斯顿太空与科学教育协会（HASSE）结业证书。
            <b>该课程一直都是一位难求，建议提前一年锁定名额。</b>
          </p>
          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align: center">
          <picture>
            <source srcset="图片/国际教育-游学、特色团3.webp" type="image/webp">
            <img src="图片/国际教育-游学、特色团3.png" class="img-fluid" alt="" style="max-width: 90%; margin: 0% 0 0 0%" />
          </picture>
        </div>

        <h5>
          &nbsp;<br />
          &nbsp;
        </h5>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 45%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">爱尔兰</h5>
            <h5>&nbsp;</h5>
          </div>
          <h2>
            <b>爱尔兰<br />
              Blackrock College & UCD 游学营</b>
          </h2>
          <h4>独家定制 “爱上爱尔兰——顶级私校大巡礼+野性大西洋之旅”</h4>
          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>
          <div class="d-flex border-bottom mb-4 pb-4" style="
                align-items: center;
                border-bottom: 0rem solid #dee2e6 !important;
              ">
            <h5>时间:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              2-3周
            </p>
          </div>

          <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center">
            <h5>对象:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              11-17周岁国际学生，可亲子同游
            </p>
          </div>

          <h5>简介：</h5>
          <p class="mb-5">
            爱尔兰是精英教育的代表、欧洲教育的天花板国家，爱尔兰顶尖私校黑石中学（Blackrock
            College）
            与都柏林大学（UCD）联合举办，在两所百年名校里一次性体验爱尔兰最高等级教育盛宴。
            同时推出独家定制<b>“爱上爱尔兰——顶级私校大巡礼+野性大西洋之旅”</b>一边考察6所爱尔兰最负盛名的私立中学，
            一边观光感受世界上最长、最美的史诗级沿海旅游路线之一，这条旅游路线爱尔兰旅游局力推十年之久，一定不会让你失望。一次奇妙之旅，
            <b>让孩子在最短时间内了解爱尔兰、爱上爱尔兰。</b>
          </p>

          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align: center">
          <picture>
            <source srcset="图片/国际教育-游学、特色团4.webp" type="image/webp">
            <img src="图片/国际教育-游学、特色团4.png" class="img-fluid" alt="" style="max-width: 80%; margin: 10% 0 0 0%" />
          </picture>
        </div>

        <h5>
          &nbsp;<br />
          &nbsp;
        </h5>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 45%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">马来西亚</h5>
            <h5>&nbsp;</h5>
          </div>
          <h1 class="display-5 mb-4">新加坡莱佛士美国学校<br /></h1>
          <h3>(Raffles American School)<br /></h3>
          <h3>马来西亚微留学</h3>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>
          <div class="d-flex border-bottom mb-4 pb-4" style="
                align-items: center;
                border-bottom: 0rem solid #dee2e6 !important;
              ">
            <h5>时间:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              1-4周不等
            </p>
          </div>

          <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center">
            <h5>对象:&nbsp;&nbsp;&nbsp;&nbsp;</h5>
            <p class="mb-5" style="
                  margin-top: 0px !important;
                  margin-bottom: 18.25px !important;
                ">
              6-16周岁国际学生（满8周岁可住校）
            </p>
          </div>

          <h5>简介：</h5>
          <p class="mb-5">
            新加坡莱佛士美国学校由亚太地区规模最大的私立教育集团——新加坡莱佛士教育集团创办，
            同时也是马来西亚最杰出的美式国际学校之一。马来西亚气候宜人，物价平宜。
            微留学的主要目的是为那些希望出国留学的孩子提供一个宝贵的机会，通过短期的留学体验，
            提前感受不同的学习方式，提升外语交流能力，并建立更强的自信心。微留学不仅让学生在卓越的美国学校课堂上获得丰富的知识，
            还提供了丰富多彩的课外活动，人文旅游观光，结识来自世界各地的朋友。<b>特别对于那些经济预算有限，
              孩子年龄尚小且缺乏海外学习和生活经验的家庭而言，马来西亚微留学绝对是不错的选择。</b>
          </p>

          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align: center">
          <picture>
            <source srcset="图片/国际教育-游学、特色团6.webp" type="image/webp">
            <img src="图片/国际教育-游学、特色团6.jpg" class="img-fluid" alt=""
              style="max-width:100%;height:auto;margin:0 auto;display:block;" />
          </picture>
        </div>
      </div>
    </div>
  </div>
  <!-- Contact Start -->
  <div class="container-fluid contact overflow-hidden py-5" style="padding-bottom: 36.5px !important">
    <div class="container py-5" style="padding-bottom: 36.5px !important">
      <div class="row g-5 mb-5" style="justify-content: flex-end; margin-bottom: 0px !important">
        <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center"></div>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 50%; margin-top: 9%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">新辉煌出国</h5>
          </div>
          <h1 class="display-5 mb-4">新辉煌国际教育升学手册</h1>

          <h5>&nbsp; &nbsp;</h5>

          <p class="mb-5">
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出国留学、培训深造、“学人之长补己之短”，是众多莘莘学子们梦寐以求的心愿。中国教育部也保持一贯支持的方针政策，明确“支持留学、鼓励回国、来去自由、发挥作用”。
          </p>
          <p class="mb-5">
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;新辉煌国际教育积极顺应社会及市场发展需求，热诚关注每一位学子的渴求，竭诚为学子们提供完善的留学服务。当下留学环境已发生巨大的变化，我们依托行业前辈十数年的专业经验和资源，提供与时俱进、全面优质的留学项目及服务。
          </p>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align:center;">
          <picture>
            <source srcset="图片/新辉煌留学世界.webp" type="image/webp">
            <img src="图片/新辉煌留学世界.png" class="img-fluid" alt=""
              style="max-width:100%;height:auto;margin:0 auto;display:block;" />
          </picture>
        </div>
      </div>
    </div>
  </div>
  <!-- ========== 主体内容区块 End ========== -->

  <!-- ========== 底部栏区块 ========== -->
  <div id="footer-placeholder"></div>
  <script>
    fetch('footer.html')
      .then(res => res.text())
      .then(html => { document.getElementById('footer-placeholder').innerHTML = html; });
  </script>
  <!-- ========== 底部栏区块 End ========== -->

  <!-- ========== 侧边悬浮导航区块 ========== -->
  <div id="custom-service-placeholder"></div>
  <script>
    fetch('components/custom-service.html')
      .then(res => res.text())
      .then(html => {
        document.getElementById('custom-service-placeholder').innerHTML = html;
      });
  </script>
  <!-- 右侧悬浮客服区块结束 -->
  <!-- ========== 侧边悬浮导航区块 End ========== -->

  <!-- ========== 本地JS依赖区块 ========== -->
  <!-- 本地JS库依赖，全部本地化 -->
  <script src="js/jquery-3.6.4.min.js"></script>
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="lib/wow/wow.min.js"></script>
  <script src="lib/easing/easing.min.js"></script>
  <script src="lib/waypoints/waypoints.min.js"></script>
  <script src="lib/counterup/counterup.min.js"></script>
  <script src="lib/owlcarousel/owl.carousel.min.js"></script>
  <script src="js/main.js"></script>
  <!-- ========== 本地JS依赖区块 End ========== -->
</body>

</html>
