﻿<!-- ========== 新辉煌出国 移民规划 Immigration.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <!-- ========== 头部meta与样式引入区块 ========== -->
    <meta charset="utf-8" />
    <title>新辉煌出国</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="" name="keywords" />
    <meta content="" name="description" />
    <link rel="shortcut icon" href="图片/新辉煌logo.png" />
    <!-- 本地字体与样式 -->
    <link href="css/responsive.css" rel="stylesheet" />
    <link href="css/css4/gg1.css" rel="stylesheet" />
    <link href="css/bootstrap.min.css" rel="stylesheet" />
    <link href="css/style.css" rel="stylesheet" />
    <link rel="stylesheet" href="css/fontawesome/all.css" />
    <link href="css/bootstrap-icons/bootstrap-icons.css" rel="stylesheet" />
    <link href="lib/animate/animate.min.css" rel="stylesheet" />
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />
    <style>
      .style2 {
        display: none;
      }
    </style>
    <style>
      .hover:hover {
        color: var(--bs-secondary) !important;
      }
    </style>
    <!-- ========== 头部meta与样式引入区块 End ========== -->
  </head>

  <body>
    <!-- ========== 顶部加载动画区块 ========== -->
    <div
      id="spinner"
      class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center"
    >
      <div
        class="spinner-border text-secondary"
        style="width: 3rem; height: 3rem"
        role="status"
      >
        <span class="sr-only">加载中...</span>
      </div>
    </div>
    <!-- ========== 顶部加载动画区块 End ========== -->

    <!-- ========== 顶部栏区块 ========== -->
    <div class="container-fluid bg-primary px-5 d-none d-lg-block">
      <div class="row gx-0 align-items-center">
        <div class="col-lg-5 text-center text-lg-start mb-lg-0">
          <div class="d-flex"></div>
        </div>
        <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
        <div class="col-lg-4 text-center text-lg-end">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
      </div>
    </div>
    <!-- ========== 顶部栏区块 End ========== -->

    <!-- ========== 导航栏区块 ========== -->
    <div class="container-fluid nav-bar p-0">
      <nav
        class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0"
      >
        <a href="" class="navbar-brand p-0">
          <h1 class="display-5 text-secondary m-0">
            <picture>
              <source srcset="图片/logo.webp" type="image/webp" />
              <img
              src="图片/logo.png"
              class="img-fluid"
              alt=""
              style="max-height: 80px"
            />
            </picture>
          </h1>
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarCollapse"
        >
          <span class="fa fa-bars"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
          <div class="navbar-nav ms-auto py-0">
            <a href="index.html" class="nav-item nav-link">首页</a>
            <a href="about.html" class="nav-item nav-link">关于我们</a>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link active"
                data-bs-toggle="dropdown"
                onclick="window.location.href='Immigration.html'"
                ><span class="dropdown-toggle">移民项目</span></a
              >
              <div class="dropdown-menu m-0">
                <a href=" Immigration-USA.html" class="dropdown-item">美国</a>
                <a href="Immigration-Canada.html" class="dropdown-item">加拿大</a>
                <a href="Immigration-Ireland.html" class="dropdown-item">爱尔兰</a>
                <a href="Immigration-HongKong.html" class="dropdown-item">香港</a>
                <a href="Immigration-SingaporeEP.html" class="dropdown-item"
                  >新加坡</a
                >
                <a href="Immigration-Grenada.html" class="dropdown-item"
                  >格林纳达</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#jiaoyu'"
                ><span class="dropdown-toggle">国际教育</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item"
                  >留学申请</a
                >
                <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item"
                  >线上课程</a
                >
                <a href="InternationalEducation-TopSchools.html" class="dropdown-item"
                  >名校直通车计划</a
                >
                <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item"
                  >语言课程</a
                >
                <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item"
                  >游学、特色团</a
                >
                <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item"
                  >国际学校</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#haiwai'"
                ><span class="dropdown-toggle">海外置业</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
                <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
                <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
                <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#pingtai'"
                ><span class="dropdown-toggle">平台合作</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item"
                  >合和法律平台</a
                >
                <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item"
                  >精英留学平台</a
                >
                <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item"
                  >新辉煌金融平台</a
                >
              </div>
            </div>
            <a href="contact.html" class="nav-item nav-link">联系我们</a>
          </div>
        </div>
      </nav>
    </div>
    <!-- ========== 导航栏区块 End ========== -->

    <!-- ========== 头图区块 ========== -->
    <div class="container-fluid bg-breadcrumb">
      <div class="container text-center py-5" style="max-width: 900px">
        <h3
          class="text-white display-3 mb-4 wow fadeInDown"
          data-wow-delay="0.1s"
        >
          移民规划
        </h3>
        <ol
          class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown"
          data-wow-delay="0.3s"
        >
          <li class="breadcrumb-item">
            <a href="index.html" class="text-white">主页</a>
          </li>
          <li class="breadcrumb-item active text-secondary">移民规划</li>
        </ol>
      </div>
    </div>
    <!-- ========== 头图区块 End ========== -->

    <!-- ========== 搜索弹窗区块 ========== -->
    <div
      class="modal fade"
      id="searchModal"
      tabindex="-1"
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content rounded-0">
          <div class="modal-header">
            <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">
              搜索
            </h4>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body d-flex align-items-center">
            <div class="input-group w-75 mx-auto d-flex">
              <input
                type="search"
                class="form-control p-3"
                placeholder="输入关键词搜索"
                aria-describedby="search-icon-1"
              />
              <span id="search-icon-1" class="input-group-text p-3"
                ><i class="fa fa-search"></i
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 搜索弹窗区块 End ========== -->

    <!-- ========== 国家服务区块 ========== -->
    <div class="container-fluid country overflow-hidden py-5">
      <div class="container">
        <div
          class="section-title text-center wow fadeInUp"
          data-wow-delay="0.1s"
          style="margin-bottom: 70px"
        >
          <div class="sub-style">
            <h5 class="sub-title text-primary px-3">移民规划</h5>
          </div>
          <h1 class="display-5 mb-4">
            我们提供以下国家和地区的<br />移民和签证服务
          </h1>
          <p class="mb-0">
            新辉煌出国提供海外源头资源的移民项目，多为中外直营。<br />
            所有投资项目均可实地考察，真实透明！
          </p>
        </div>
        <div class="row g-4 text-center">
          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 70%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  香港
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/香港.webp" type="image/webp" />
                  <img
                  src="图片/香港.jpg"
                  class="img-fluid w-100 rounded"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/香港旗帜.webp" type="image/webp" />
                  <img
                  src="图片/香港旗帜.jpg"
                  class="img-fluid rounded-circle"
                  alt="Image"
                />
                </picture>
              </div>

              <div class="country-name" style="text-align: justify; width: 80%">
                <a style="color: #ffffff" href="Immigration-HongKong.html">
                  <h2
                    align="center"
                    class=""
                    style="color: #ffffff; font-weight: bold"
                  >
                    <br />香港<br />
                  </h2>
                  <b>新资本投资者入境计划（NEW CIES）：</b
                  >“王者项目”时隔8年再度重启；投资3000万港币，精心打造保底投资方案供您选择。
                  <br /><a class="hover" style="color: #ffffff">了解更多→</a>
                </a>
              </div>
            </div>
          </div>

          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.1s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 70%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  美国
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/美国.webp" type="image/webp" />
                  <img
                  src="图片/美国.jpg"
                  class="img-fluid w-100 rounded"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/美国旗帜.webp" type="image/webp" />
                  <img
                  src="图片/美国旗帜.jpg"
                  class="img-fluid rounded-circle"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a href=" Immigration-USA.html" class="" style="color: #ffffff">
                  <h2 align="center" style="color: #ffffff; font-weight: bold">
                    <br />美国<br />
                  </h2>
                  <b>EB-5直投：</b
                  >无语言、无排期、90天快速获批“小绿卡”；绿卡保障、商业透明、绝无“区域中心诈骗”风险，最快2年退出。
                  <br /><a class="hover" style="color: #ffffff">了解更多→</a>
                </a>
              </div>
            </div>
          </div>

          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.3s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 80%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  加拿大
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/加拿大.webp" type="image/webp" />
                  <img
                  src="图片/加拿大.jpg"
                  class="img-fluid w-100 rounded"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/加拿大旗帜.webp" type="image/webp" />
                  <img
                  src="图片/加拿大旗帜.jpg"
                  class="img-fluid rounded-circle"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a href="Immigration-Canada.html" class="" style="color: #ffffff">
                  <h2 align="center" style="color: #ffffff; font-weight: bold">
                    <br />加拿大<br />
                  </h2>
                  <b>联邦创投（SUV）：</b
                  >联邦项目，仅需雅思5分，全家一步到位拿枫叶卡；国家级主创项目，国内多基地考察，投资款有返还，费用更低。。
                  <br /><a class="hover" style="color: #ffffff">了解更多→</a>
                </a>
              </div>
            </div>
          </div>
          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.5s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 80%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  爱尔兰
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/爱尔兰.webp" type="image/webp" />
                  <img
                  src="图片/爱尔兰.jpg"
                  class="img-fluid w-100 rounded"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/爱尔兰旗帜.webp" type="image/webp" />
                  <img
                  src="图片/爱尔兰旗帜.jpg"
                  class="img-fluid rounded-circle"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a href="Immigration-Ireland.html" class="" style="color: #ffffff">
                  <h2 align="center" style="color: #ffffff; font-weight: bold">
                    <br />爱尔兰<br />
                  </h2>
                  <b>高技能工作居留：</b
                  >无需雅思，快至2个月获批，21个月转永居；爱尔兰本土集团化公司源头雇主直聘，免费安家服务大礼包，不成功全退费。
                  <br /><a class="hover" style="color: #ffffff">了解更多→</a>
                </a>
              </div>
            </div>
          </div>
          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.7s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 90%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  第三国身份
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/格林纳达.webp" type="image/webp" />
                  <img
                  src="图片/格林纳达.jpg"
                  class="img-fluid w-100 rounded"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/格林纳达旗帜.webp" type="image/webp" />
                  <img
                  src="图片/格林纳达旗帜.jpg"
                  class="img-fluid rounded-circle"
                  alt="Image"
                />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a
                  href="Immigration-Grenada.html"
                  class=""
                  style="color: #ffffff"
                >
                  <h2 align="center" style="color: #ffffff; font-weight: bold">
                    <br />第三国身份<br />
                  </h2>
                  格林纳达护照——英联邦护照、“美国后花园”、美元资产配置身份首选、世界知名医学院可英/美/加执业、E2签证快速登陆美国、与中国互免。
                  <br /><a class="hover" style="color: #ffffff">了解更多→</a>
                </a>
              </div>
            </div>
          </div>

          <div class="col-12">
            <a
              class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
              data-wow-delay="0.1s"
              href="Immigration-SingaporeEP.html"
              >更多国家</a
            >
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 国家服务区块 End ========== -->

    <!-- ========== 核心优势区块 ========== -->
    <!-- 保留原有外层结构 -->
    <div class="container-fluid counter-facts py-5">
      <div id="counter-features-container"></div>
    </div>
    <script>
      fetch("components/counter-features.html")
        .then((res) => res.text())
        .then((html) => {
          document.getElementById("counter-features-container").innerHTML =
            html;
        });
    </script>
    <!-- ========== 核心优势区块 End ========== -->

    <!-- ========== 底部栏区块 ========== -->
    <div id="footer-placeholder"></div>
    <script>
      fetch("footer.html")
        .then((res) => res.text())
        .then((html) => {
          document.getElementById("footer-placeholder").innerHTML = html;
        });
    </script>
    <!-- ========== 底部栏区块 End ========== -->

    <!-- ========== 悬浮客服区块 ========== -->
    <div id="custom-service-placeholder"></div>
    <script>
      fetch("components/custom-service.html")
        .then((res) => res.text())
        .then((html) => {
          document.getElementById("custom-service-placeholder").innerHTML =
            html;
        });
    </script>
    <!-- ========== 悬浮客服区块 End ========== -->

    <!-- ========== 本地JS依赖区块 ========== -->
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/counterup/counterup.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="js/main.js"></script>
    <!-- ========== 本地JS依赖区块 End ========== -->
  </body>
</html>

