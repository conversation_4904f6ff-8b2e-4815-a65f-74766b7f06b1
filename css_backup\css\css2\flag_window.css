.flex_window {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: inline-block;
    text-align: left;


}
.flex_window dt{
    margin-right: 10px;
    font-size: 13px;
    color: #494949;
    display: inline-block;
}
.flex_window dd .select-i-window:not(:last-of-type) {
    margin-right: 30px;
}
.select-i-window{
    position: relative;
    width: 196px;
}
.select-i-window .input-txt-window{
    vertical-align: middle;
    box-sizing: border-box;
    display: inline-block;
    margin-left: -12px;
    text-indent: 10px;
    width: 100%;
    height: 36px;
    line-height: 36px;
    border-radius: 5px;
    font-size: 13px;
    color: rgba(80, 80, 80, .8);


    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    cursor: pointer;
    border: 1px solid #e8e8e8;
}
.jt_window{
    position: absolute;
    width: 12px;
    height: 12px;
    top:14px;
    right: 24px;
    vertical-align: middle;
    cursor: pointer;
}

.ul-select-window{
    box-sizing: border-box;
    padding-left: 10px;
    display: block;
    position: absolute;
    top:38px;
    left: -12px;
    width: 196px;
    max-height: 240px;
    overflow-y:auto;
    z-index: 1;
    font-size: 0;
    border-radius: 5px;
    background-color: #fff;
}
.ul-select-window.un-fold-window{
    display: none;
}
.ul-select-window li:last-child{
    border-bottom: none;
}
.ul-select-window li {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    margin-top: 12px;
    font-size: 12px;
    color: rgba(73, 73, 73, 0.9);
    text-align: left;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.ul-select-window li strong{
    font-weight: 400;
    margin-left: 10px;
}

.ul-select-window li:hover{
    color: #000;
}

/* 设置滚动条的样式 */
.ul-select-window::-webkit-scrollbar {
    width:5px;
    height: 5px;

}
    /* 滚动槽 */
.ul-select-window::-webkit-scrollbar-track {
    /* -webkit-box-shadow:inset006pxrgba(0,0,0,0.3); */
    border-radius:3px;
    background-color: #d2d2d2;
}
    /* 滚动条滑块 */
.ul-select-window::-webkit-scrollbar-thumb {
    border-radius:3px;
    height: 5px;
    background-color: #8b8b8b;
    /* -webkit-box-shadow:inset006pxrgba(0,0,0,0.5); */
 }

 .ul-select li:last-of-type {
    padding-bottom: 8px;
}