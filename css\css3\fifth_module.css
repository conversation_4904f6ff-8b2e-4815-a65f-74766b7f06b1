@charset "gb2312" ;
.fifth-module {
	width:1200px;
	margin:0 auto;
}

/*777888*/

.fhm-header{
	overflow:hidden;
}
.fhm-tt {
	margin-top:-20px;
	width:299px;
	height:93px;
	background: url(/static/index/images/customer/consulting.png) no-repeat left 54px;
}
.fhm-more {
	width:701px;
	height:73px;
}
.fhm-ul-t {
	width:95%;
	padding-left:5%;
	height:46px;
	padding-top:18px;
}
.fhm-li-t {
	width:auto;
	height:28px;
	line-height:28px;
	float:left;
	text-align:center;
	padding: 0 7px;
	font-size: 15px;
}
.fhm-a-t {
	color:rgba(39,28,28,1);
}
.fhm-a-cur {
	width:100%;
	height:100%;
	border-radius:5px;
	color:rgba(255,255,255,1);
	background:#a67e3d;
}
.fhm-corresponding {
	width:100%;
	height:240px;
	overflow:hidden;
}
.fhm-c-list {
	width:100%;
	height:240px;
	display:none;
	background:#fff;
}
.fhm-cl-ul {
	height:240px;
	top:0;
	left:0;
}
.fhm-cl-li {
	width:142px;
	margin-left:20px;
	height:230px;
	margin-top:2px;
	float:left;
	background:#fff;
	cursor:pointer;
	box-shadow: 0px 0px 4px rgba(34,34,34,.64);

}
.fhm-cl-p {
	width:373px;
	height:138px;
	background:#fff;
	top:0;
	z-index:2;
	display:none;
	padding:10px 0 0 10px;
	box-shadow: 0px 0px 4px rgba(34,34,34,.64);
	height:220px !important;
}
.fhm-cl-p img {
	width:119px;
	height:119px;
	margin-right:10px;
}
.fhm-cl-p p {
    width: 220px;
    height: 124px;
    color: #a67e3d;
    overflow: hidden;
    margin-top: -3px;
    line-height: 21px;
}
.fhm-cl-t {
	width:135px;
	height:130px;
	overflow:hidden;
	text-align:center;
}
.fhm-cl-t img {
	width:135px;
	height:130px;
	text-align:center;

}
.fhm-cl-b {
	display: block;
	width:98%;
	height:92px;
	background:#fff;
	margin: 0 1%;
}
.fhm-cl-b p {
	color:#a67e3d;
	font-size:14px;
	margin:0 auto;
	text-align:center;
	overflow: hidden;
	white-space: nowrap;
	font-family:Arial, "microsoft yahei";
}
.fhm-cl-b li {
	width:100px;
	height:28px;
	line-height:30px;
	border-radius:5px;
	background-color:#a67e3d;
	margin:5px auto 0;
}
.fhm-cl-b li.cur {
	background-color:#c5c5c5;
}
.fhm-cl-b li a {
	color:rgba(245,234,214,1);
}
.fhm-cl-b li:first-child a {
	text-indent:30px;
	background:url(/static/index/images/customer/weixin.png) no-repeat;
	background-position: 8px 8px;
}
.fhm-cl-b li:first-child a.cur {
	color:#fff;
	text-indent:30px;
	background:url(/static/index/images/customer/zaixian.png) no-repeat;
	background-position: 8px 8px;
}
.fhm-cl-b li:last-child a {
	text-indent:25px;
	background:url(/static/index/images/customer/qq.png) no-repeat;
	background-position: 8px 7px;
}

.dbk {
	display:block!important;
}
.mask {
	width:100%;
	background:rgba(0,0,0,.5);
	position:fixed;
	top:0;
	left:0;
	z-index:9999;
	display:none;
}
.mask-in {
	width:671px;
	height:298px;
	background:url(/statics/images/bg_03.jpg) no-repeat left top;
	top:50%;
	left:50%;
	padding:20px 0 0 0;
	margin:-159px 0 0 -335.5px;

}
.mask-in form {
	width:100%;
	height:34px;
}
.text-in {
	outline:none;
	margin: 0 15px 0 180px;
	width:188px;
	height:32px;
	border:1px solid rgba(204,204,204,1);
	padding:0 0 0 5px ;
}
.btn-in {
	width:100px;
	height:32px;
	line-height:32px;
	color:rgba(255,255,255,1);
	font-size:16px;
	font-weight:700;
	background:rgba(60,24,2,1);
	margin-top:1px;
}
.close {
	width:20px;
	height:20px;
	background:url(/statics/images/tc_03.jpg) no-repeat center;
	right:5px;
	top:5px;
}
.mask-p {
	text-indent:30px;
	font-size:14px;
	color:rgba(102,102,102,1);
}
.mask-p em {
	color:rgba(152,0,0,1);

}
.mask-bg-bg {
	width:100%;
	height:193px;
	background:url(/static/index/images/customer/bg_bg_bg.jpg) no-repeat center center;
	bottom:0;
	left:0;
}













