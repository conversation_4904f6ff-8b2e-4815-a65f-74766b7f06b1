﻿<!-- ========== 新辉煌出国 Immigration-Grenada.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <!-- ========== 头部meta与样式引入区块 ========== -->
    <meta charset="utf-8" />
    <title>新辉煌出国</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="" name="keywords" />
    <meta content="" name="description" />
    <!-- 兜底样式，移动端适配 -->
    <link href="css/responsive.css" rel="stylesheet" />
    <!-- 本地图标字体，已本地化 -->
    <link rel="stylesheet" href="css/fontawesome/all.css" />
    <link rel="stylesheet" href="css/bootstrap-icons/bootstrap-icons.css" />

    <!-- 本地动画库、轮播库样式 -->
    <link href="lib/animate/animate.min.css" rel="stylesheet" />
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />

    <!-- 本地Bootstrap样式 -->
    <link href="css/bootstrap.min.css" rel="stylesheet" />

    <!-- 站点主样式，结构化注释 -->
    <link href="css/style.css" rel="stylesheet" />



    <!-- 结构化区块样式 -->
    <link rel="stylesheet" href="./css/merged/reset1.css" />
    <link rel="stylesheet" href="./css/merged/common1.css" />
    <link rel="stylesheet" href="./css/merged/window1.css" />
    <link rel="stylesheet" href="./css/merged/advisers1.css" />
    <link rel="stylesheet" href="./css/merged/flag_window.css" />
    <link rel="stylesheet" href="./css/merged/flag.css" />
    <link rel="stylesheet" href="./css/merged/swiper.css" />
    <link rel="stylesheet" href="./css/merged/globe_subject.css" />
    <link href="css/css4/gg1.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/merged/public.css" />
    <link rel="stylesheet" href="./css/merged/index_new.css" />
    <link rel="stylesheet" href="./css/merged/project_page_cq_new.css" />
    <link rel="stylesheet" href="./css/merged/jinrErweima.css" />
    <link rel="stylesheet" href="./css/merged/flag(1).css" />

    <link rel="shortcut icon" href="图片/新辉煌logo.png" />

    <style>
      .Breadcrumbs{
        height:auto !important;
      }
      /* 隐藏style2样式 */
      .style2 {
        display: none;
      }
      .hover:hover {
        color: var(--bs-secondary) !important;
      }
    </style>
    <!-- ========== 头部meta与样式引入区块 End ========== -->
  </head>

  <body>
    <!-- ========== 顶部加载动画区块 ========== -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
      <div class="spinner-border text-secondary" style="width: 3rem; height: 3rem" role="status">
        <span class="sr-only">加载中...</span>
      </div>
    </div>
    <!-- 顶部加载动画区块结束 -->

    <!-- ========== 顶部栏区块 ========== -->
    <div class="container-fluid bg-primary px-5 d-none d-lg-block">
      <div class="row gx-0 align-items-center">
        <div class="col-lg-5 text-center text-lg-start mb-lg-0">
          <div class="d-flex"></div>
        </div>
        <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
        <div class="col-lg-4 text-center text-lg-end">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
      </div>
    </div>
    <!-- 顶部栏区块结束 -->

    <!-- ========== 导航栏区块 ========== -->
    <!-- 头图区块 -->
    <div class="container-fluid nav-bar p-0">
        <nav
          class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0"
        >
          <a href="" class="navbar-brand p-0">
            <h1 class="display-5 text-secondary m-0">
              <picture>
                <source srcset="图片/logo.webp" type="image/webp" />
                <img
                src="图片/logo.png"
                class="img-fluid"
                alt=""
                style="max-height: 80px"
              />
              </picture>
            </h1>
            <!-- <img src="img/logo.png" alt="Logo"> -->
          </a>
          <button
            class="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarCollapse"
          >
            <span class="fa fa-bars"></span>
          </button>
          <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto py-0">
              <a href="index.html" class="nav-item nav-link">首页</a>
              <a href="about.html" class="nav-item nav-link">关于我们</a>
              <div class="nav-item dropdown">
                <a href="Immigration.html" class="nav-link active" data-bs-toggle="dropdown" onclick="window.location.href='Immigration.html'">
                <span class="dropdown-toggle">移民项目</span>
              </a>
                <div class="dropdown-menu m-0">
                  <a href=" Immigration-USA.html" class="dropdown-item"
                    >美国</a
                  >
                  <a href="Immigration-Canada.html" class="dropdown-item"
                    >加拿大</a
                  >
                  <a href="Immigration-Ireland.html" class="dropdown-item"
                    >爱尔兰</a
                  >
                  <a href="Immigration-HongKong.html" class="dropdown-item"
                    >香港</a
                  >
                  <a href="Immigration-SingaporeEP.html" class="dropdown-item"
                    >新加坡</a
                  >
                  <a href="Immigration-Grenada.html" class="dropdown-item active"
                    >格林纳达</a
                  >
                </div>
              </div>
            </div>
              <div class="nav-item dropdown">
                <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#jiaoyu'"
                ><span class="dropdown-toggle">国际教育</span></a
              >
                <div class="dropdown-menu m-0">
                  <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item"
                    >留学申请</a
                  >
                  <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item"
                    >线上课程</a
                  >
                  <a href="InternationalEducation-TopSchools.html" class="dropdown-item"
                    >名校直通车计划</a
                  >
                  <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item"
                    >语言课程</a
                  >
                  <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item"
                    >游学、特色团</a
                  >
                  <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item"
                    >国际学校</a
                  >
                </div>
              </div>
              <div class="nav-item dropdown">
                <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#haiwai'"
                ><span class="dropdown-toggle">海外置业</span></a
              >
                <div class="dropdown-menu m-0">
                  <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
                  <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
                  <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
                  <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
                </div>
              </div>
              <div class="nav-item dropdown">
                <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#pingtai'"
                ><span class="dropdown-toggle">平台合作</span></a
              >
                <div class="dropdown-menu m-0">
                  <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item"
                    >合和法律平台</a
                  >
                  <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item"
                    >精英留学平台</a
                  >
                  <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item"
                    >新辉煌金融平台</a
                  >
                </div>
              </div>
              <a href="contact.html" class="nav-item nav-link">联系我们</a>
            </div>
          </div>
        </nav>
      </div>
    <!-- 头图区块结束 -->
    <!-- ========== 导航栏区块 End ========== -->

    <!-- ========== 搜索弹窗区块 ========== -->
    <!-- Modal Search Start -->
    <div
      class="modal fade"
      id="searchModal"
      tabindex="-1"
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content rounded-0">
          <div class="modal-header">
            <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">
              搜索
            </h4>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body d-flex align-items-center">
            <div class="input-group w-75 mx-auto d-flex">
              <input
                type="search"
                class="form-control p-3"
                placeholder="输入关键词搜索"
                aria-describedby="search-icon-1"
              />
              <span id="search-icon-1" class="input-group-text p-3"
                ><i class="fa fa-search"></i
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Modal Search End -->
    <!-- ========== 搜索弹窗区块 End ========== -->

    <!-- ========== 主体内容区块 ========== -->
    <!-- Header Start -->
    <div
      class="container-fluid bg-breadcrumb"
      style="
        background: linear-gradient(
            rgba(0, 58, 102, 0.9),
            rgba(0, 58, 102, 0.8)
          ),
          url(./图片/头图-格林纳达.jpg);
        background-position: center center;
        background-repeat: no-repeat;
        background-attachment: initial;
        background-size: cover;
        padding: 100px 0 60px 0;
      "
    >
      <div class="container text-center py-5" style="max-width: 900px">
        <h3
          class="text-white display-3 mb-4 wow fadeInDown"
          data-wow-delay="0.1s"
        >
          格林纳达投资入籍计划<br />（捐款）
        </h3>

        <ol
          class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown"
          data-wow-delay="0.3s"
        >
          <li class="breadcrumb-item">
            <a href="index.html" class="text-white">主页</a>
          </li>

          <li class="breadcrumb-item active text-secondary">格林纳达移民</li>
        </ol>
      </div>
    </div>
    <!-- Header End -->

    <!-- 头部 -->

    <div class="Breadcrumbs">
      <div class="width">
        <h3>格林纳达投资入籍计划</h3>
        <div class="b-content clearfix" style="position: relative">
          <div class="pull-left b-left">
            <p class="two">
              格林纳达位于北美洲，属英联邦成员国，可实现四代移民。
              随行人员不限父母年龄，同时30岁以下超大龄子女以及年满18岁以上的兄弟姐妹也可随行。
              整体周期7-11个月。通过格林纳达投资入籍计划，申请人可自由畅行168国和地区，其中英国6个月，
              申根国/欧盟90天，港澳90天;格林纳达与中国签署了互免协定，可享受单次免签30天。申请要求简单，全程无需登陆。
              年龄18岁以上，无犯罪记录，选择捐款23.5万美金以上，或购买格林纳达政府批准的价值27万美金的项目即可。
            </p>
            <div style="display: flex; flex-wrap: nowrap">
              <a
              href="contact.html#contact"
                class="pmq text-right"
                style="display: block; margin: 42px 100px 0 0"
                >在线咨询</a
              >
              <a
              href="contact.html#contact"
                class="pmq text-right"
                style="
                  display: block;
                  width: 132px;
                  height: 38px;
                  color: #fff;
                  background-color: #a67e3d;
                  line-height: 38px;
                  font-size: 16px;
                  margin-top: 42px;
                  border-radius: 19px;
                  text-align: center;
                "
                >联系我们</a
              >
            </div>
            >
          </div>

          <div class="pull-left b-right gs_office_imgs">
            <picture>
              <source srcset="./css/css2/img/格林纳达移民1.webp" type="image/webp" />
              <img
              src="./css/css2/img/格林纳达移民1.jpg"
              style="display: block"
            />
            </picture>
            <!-- <img src="/static/index/images/project/b-right-video-btn.png" alt="" class="b-right-video-btn"> -->
            <div class="b-right-items">
              <div class="b-right-items-info">
                <img
                  style="
                    width: 24px;
                    height: 21px;
                    border-radius: unset;
                    display: block;
                  "
                  data-original="css/css2/img/project_blzq.png"
                  src="./css/css2/img/project_blzq.png"
                  alt=""
                />
                <p>办理周期</p>
                <p>7-11个月</p>
              </div>
              <div class="b-right-items-info">
                <img
                  style="
                    width: 34px;
                    height: 20px;
                    border-radius: unset;
                    display: block;
                  "
                  data-original="css/css2/img/project_hdsf.png"
                  src="./css/css2/img/project_hdsf.png"
                  alt=""
                />
                <p>获得身份</p>
                <p>投资入籍</p>
              </div>
              <div class="b-right-items-info">
                <img
                  style="
                    width: 24px;
                    height: 21px;
                    border-radius: unset;
                    display: block;
                  "
                  data-original="css/css2/img/project_zcyq.png"
                  src="./css/css2/img/project_zcyq.png"
                  alt=""
                />
                <p>资产要求</p>
                <p>200万人民币</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>



    <div class="Breadcrumbs">
      <div class="width">
        <h3>格林纳达投资入籍计划申请条件</h3>
        <div class="b-content clearfix" style="position: relative">
          <div class="pull-left b-left">
            <p class="two">
              1.主申请人需年满18周岁;2.无犯罪记录; 3.两种入籍模式:
              1)捐款模式:1-4人申请捐款23.5万美金，5人及以上申请每增加一人需多捐款2.5万美金。
              如申请人中包括有未满55周岁的父母/祖父母需另加5万美金/人，如包含18周岁以上未婚无子女的兄弟姐妹需另加7.5万美金/人。
              2)购房模式:申请获批后，投资人需投资格林纳达官方指定的价值270,000美金的共有产权项目。
            </p>
            <div style="display: flex; flex-wrap: nowrap">
              <a
              href="contact.html#contact"
                class="pmq text-right"
                style="display: block; margin: 42px 100px 0 0"
                >在线咨询</a
              >
              <a
              href="contact.html#contact"
                class="pmq text-right"
                style="
                  display: block;
                  width: 132px;
                  height: 38px;
                  color: #fff;
                  background-color: #a67e3d;
                  line-height: 38px;
                  font-size: 16px;
                  margin-top: 42px;
                  border-radius: 19px;
                  text-align: center;
                "
                >联系我们</a
              >
            </div>
          </div>

          <div class="pull-left b-right gs_office_imgs">
            <picture>
              <source srcset="./css/css2/img/格林纳达移民2.webp" type="image/webp" />
              <img
              src="./css/css2/img/格林纳达移民2.jpg"
              style="display: block"
              />
            </picture>

            <!-- <img src="/static/index/images/project/b-right-video-btn.png" alt="" class="b-right-video-btn"> -->
          </div>
        </div>
      </div>
    </div>

    <!-- 格林纳达投资入籍计划流程 组件化引入区块 -->
    <div id="process-grenada-container"></div>
    <script>
      fetch('components/process-grenada.html')
        .then(res => res.text())
        .then(html => {
          document.getElementById('process-grenada-container').innerHTML = html;
        });
    </script>
    <!-- 格林纳达投资入籍计划流程区块结束 -->

    <!-- 格林纳达入籍优势区块 -->
    <div class="application">
      <div class="width">
        <div class="conditions clearfix">
          <div class="pull-left conditions-left">
            <!-- <a href="/ympg.html" target="_blank"> -->
            <picture>
              <source srcset="./css/css2/img/加拿大移民3.webp" type="image/webp" />
              <img src="./css/css2/img/加拿大移民3.jpg" />
            </picture>
            <!-- </a> -->
          </div>
          <div class="pull-right conditions-right">
            <h3>格林纳达入籍为什么选择新辉煌？<br />&nbsp;</h3>
            <h4>专业甄选，与政府指定机构深度合作；<br />&nbsp;</h4>
            <h4>中美团队，共同为您提供优质递案服务；<br />&nbsp;</h4>
            <h4>专注捐款，严守法案拒绝虚假投资入籍。<br />&nbsp;</h4>
          </div>
        </div>
      </div>
    </div>

    <h2>
      &nbsp;<br />
      &nbsp;<br />
    </h2>
    <h2>
      &nbsp;<br />
      &nbsp;<br />
    </h2>

  <!-- ========== 底部栏区块 ========== -->
  <div id="footer-placeholder"></div>
  <script>
    fetch('footer.html')
      .then(res => res.text())
      .then(html => { document.getElementById('footer-placeholder').innerHTML = html; });
  </script>
  <!-- 底部栏区块结束 -->
  <!-- ========== 底部栏区块 End ========== -->

    <!-- ========== 侧边悬浮导航区块 ========== -->
    <!-- 右侧悬浮客服区块 -->
    <div id="custom-service-placeholder"></div>
    <script>
      fetch('components/custom-service.html')
        .then(res => res.text())
        .then(html => {
          document.getElementById('custom-service-placeholder').innerHTML = html;
        });
    </script>
    <!-- 右侧悬浮客服区块结束 -->
    <!-- ========== 侧边悬浮导航区块 End ========== -->

    <!-- ========== 本地JS依赖区块 ========== -->
    <!-- 本地JS库依赖，全部本地化 -->
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/counterup/counterup.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="js/main.js"></script>
    <!-- ========== 本地JS依赖区块 End ========== -->
  </body>
</html>

