﻿<!-- ========== 新辉煌出国 InternationalEducation-StudyAbroadApplication.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <!-- ========== 头部meta与样式引入区块 ========== -->
    <meta charset="utf-8" />
    <title>新辉煌出国</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="" name="keywords" />
    <meta content="" name="description" />
    <link href="css/responsive.css" rel="stylesheet" />
    <!-- 本地图标字体，已本地化 -->
    <link rel="stylesheet" href="css/fontawesome/all.css" />
    <link rel="stylesheet" href="css/bootstrap-icons/bootstrap-icons.css" />

    <!-- 本地动画库、轮播库样式 -->
    <link href="lib/animate/animate.min.css" rel="stylesheet" />
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />

    <!-- 结构化区块样式 -->
    <link href="css/css4/gg1.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/merged/reset1.css" />
    <link rel="stylesheet" href="./css/merged/common1.css" />
    <link rel="stylesheet" href="./css/merged/window1.css" />
    <link rel="stylesheet" href="./css/merged/advisers1.css" />
    <link rel="stylesheet" href="./css/merged/flag_window.css" />
    <link rel="stylesheet" href="./css/merged/flag.css" />
    <link rel="stylesheet" href="./css/merged/swiper.css" />
    <link rel="stylesheet" href="./css/merged/globe_subject.css" />
    <link rel="stylesheet" href="./css/merged/public.css" />
    <link rel="stylesheet" href="./css/merged/index_new.css" />
    <link rel="stylesheet" href="./css/merged/project_page_cq_new.css" />
    <link rel="stylesheet" href="./css/merged/jinrErweima.css" />
    <link rel="stylesheet" href="./css/merged/flag(1).css" />

    <!-- 本地Bootstrap样式 -->
    <link href="css/bootstrap.min.css" rel="stylesheet" />

    <!-- 站点主样式，结构化注释 -->
    <link href="css/style.css" rel="stylesheet" />
 

    <link rel="shortcut icon" href="图片/新辉煌logo.png" />

    <style>
      /* 隐藏style2样式 */
      .style2 {
        display: none;
      }
      .hover:hover {
        color: var(--bs-secondary) !important;
      }
    </style>
    <!-- ========== 头部meta与样式引入区块 End ========== -->
  </head>

  <body>
    <!-- ========== 顶部加载动画区块 ========== -->
    <div
      id="spinner"
      class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center"
    >
      <div
        class="spinner-border text-secondary"
        style="width: 3rem; height: 3rem"
        role="status"
      >
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <!-- 顶部加载动画区块结束 -->

    <!-- 顶部栏区块 -->
    <div class="container-fluid bg-primary px-5 d-none d-lg-block">
      <div class="row gx-0 align-items-center">
        <div class="col-lg-5 text-center text-lg-start mb-lg-0">
          <div class="d-flex"></div>
        </div>
        <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
        <div class="col-lg-4 text-center text-lg-end">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
      </div>
    </div>
    <!-- 顶部栏区块结束 -->

    <!-- 导航栏区块 -->
    <div class="container-fluid nav-bar p-0">
      <nav
        class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0"
      >
        <a href="" class="navbar-brand p-0">
          <h1 class="display-5 text-secondary m-0">
            <picture>
              <source srcset="图片/logo.webp" type="image/webp">
              <img src="图片/logo.png" class="img-fluid" alt="" style="max-height: 80px" />
            </picture>
          </h1>
          <!-- <img src="img/logo.png" alt="Logo"> -->
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarCollapse"
        >
          <span class="fa fa-bars"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
          <div class="navbar-nav ms-auto py-0">
            <a href="index.html" class="nav-item nav-link">首页</a>
            <a href="about.html" class="nav-item nav-link">关于我们</a>
            <div class="nav-item dropdown">
              <a href="Immigration.html" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='Immigration.html'">
                <span class="dropdown-toggle">移民项目</span>
              </a>
              <div class="dropdown-menu m-0">
                <a href=" Immigration-USA.html" class="dropdown-item"
                  >美国</a
                >
                <a href="Immigration-Canada.html" class="dropdown-item"
                  >加拿大</a
                >
                <a href="Immigration-Ireland.html" class="dropdown-item"
                  >爱尔兰</a
                >
                <a href="Immigration-HongKong.html" class="dropdown-item"
                  >香港</a
                >
                <a href="Immigration-SingaporeEP.html" class="dropdown-item"
                  >新加坡</a
                >
                <a href="Immigration-Grenada.html" class="dropdown-item"
                  >格林纳达</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a href="#" class="nav-link active" data-bs-toggle="dropdown" onclick="window.location.href='index.html#jiaoyu'"
                ><span class="dropdown-toggle">国际教育</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item active"
                  >留学申请</a
                >
                <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item"
                  >线上课程</a
                >
                <a href="InternationalEducation-TopSchools.html" class="dropdown-item"
                  >名校直通车计划</a
                >
                <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item"
                  >语言课程</a
                >
                <a
                  href="InternationalEducation-StudyTour-SpecialGroups.html"
                  class="dropdown-item"
                  >游学、特色团</a
                >
                <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item"
                  >国际学校</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#haiwai'"
                ><span class="dropdown-toggle">海外置业</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
                <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
                <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
                <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
              </div>
            </div>
            <div class="nav-item dropdown">
              <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#pingtai'"
                ><span class="dropdown-toggle">平台合作</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item"
                  >合和法律平台</a
                >
                <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item"
                  >精英留学平台</a
                >
                <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item"
                  >新辉煌金融平台</a
                >
              </div>
            </div>
            <a href="contact.html" class="nav-item nav-link">联系我们</a>
          </div>
        </div>
      </nav>
    </div>
    <!-- 导航栏区块结束 -->

    <!-- 主体内容区块：留学申请介绍等 -->
    <div
      class="container-fluid bg-breadcrumb"
      style="
        background: linear-gradient(
            rgba(0, 58, 102, 0.9),
            rgba(0, 58, 102, 0.8)
          ),
          url(./img/breadcrumb.png);
        background-position: center center;
        background-repeat: no-repeat;
        background-attachment: initial;
        background-size: cover;
        padding: 100px 0 60px 0;
      "
    >
      <div class="container text-center py-5" style="max-width: 2000px">
        <h3
          class="text-white display-3 mb-4 wow fadeInDown"
          data-wow-delay="0.1s"
        >
          海外留学国家申请<br />
        </h3>
        <h2 style="color: #ffffff">（Overseas Education Center）</h2>
        <ol
          class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown"
          data-wow-delay="0.3s"
        >
          <li class="breadcrumb-item">
            <a href="index.html" class="text-white">主页</a>
          </li>

          <li class="breadcrumb-item active text-secondary">
            海外留学国家申请
          </li>
        </ol>
      </div>
    </div>

    <!-- Contact Start -->
    <div
      class="container-fluid contact overflow-hidden py-5"
      style="padding-bottom: 1rem !important"
    >
      <div class="container py-5" style="padding-bottom: 1rem !important">
        <div
          class="row g-5 mb-5"
          style="justify-content: flex-end; margin-bottom: 0rem !important"
        >
          <div
            class="col-lg-6 wow fadeInLeft"
            data-wow-delay="0.1s"
            style="max-width: 45%"
          >
            <div class="sub-style">
              <h5 class="sub-title text-primary pe-3">海外留学</h5>
            </div>
            <h5>&nbsp;</h5>
            <h1 class="display-5 mb-4">高等留学</h1>
            <h5 class="mb-5" style="margin-bottom: 1rem !important">
              <b>• 北美地区：</b>美国、加拿大<br />
              <b>• 英联邦国家：</b>英国、澳洲、新西兰、爱尔兰<br />
              <b>• 亚洲地区：</b>香港、新加坡、马来西亚 <br />
            </h5>
            <h5>&nbsp;</h5>

            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                href="contact.html#contact"
                >了解更多</a
              >
            </div>

            <div
              class="d-flex border-bottom mb-4 pb-4"
              style="align-items: center"
            ></div>

            <h1 class="display-5 mb-4">低龄留学</h1>
            <h5 class="mb-5">
              • 爱尔兰公立学校、私立学校<br />
              • 英国顶级私立中学/贵族寄宿中学<br />
              • 新加坡政府中小学、国际学校<br />
              • 马来西亚国际学校
            </h5>

            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                href="contact.html#contact"
                >了解更多</a
              >
            </div>

            <div
              class="d-flex border-bottom mb-4 pb-4"
              style="align-items: center"
            ></div>
            <h1 class="display-5 mb-4" style="color: red;">特别推荐</h1>
            <h5 class="mb-5" style="color: red;">
              • 英国加比达斯 Gabbitas 教育集团·顶级私校入学考试 UKISET<br />
              • 新加坡政府学校人学考试培训 AEIS<br />
              • 迪拜留学
            </h5>
            
            


            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                href="contact.html#contact"
                >了解更多 ➡</a
              >
            </div>
          </div>

          <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 ">
            <picture>
              <source srcset="图片/国际教育-留学申请1.webp" type="image/webp">
              <img src="图片/国际教育-留学申请1.png" class="img-fluid" alt="" style="max-width: 100%; margin: 20% 0 0 0" />
            </picture>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Start -->
    <div
      class="container-fluid contact overflow-hidden py-5"
      style="padding-bottom: 1rem !important"
    >
      <div class="container py-5" style="padding-bottom: 1rem !important">
        <div
          class="row g-5 mb-5"
          style="justify-content: flex-end; margin-bottom: 0rem !important"
        >
          <div
            class="d-flex border-bottom mb-4 pb-4"
            style="align-items: center"
          ></div>

          <div
            class="col-lg-6 wow fadeInLeft"
            data-wow-delay="0.1s"
            style="max-width: 50%; margin-top: 9%"
          >
            <div class="sub-style">
              <h5 class="sub-title text-primary pe-3">新辉煌出国</h5>
            </div>
            <h1 class="display-5 mb-4">新辉煌国际教育升学手册</h1>

            <h5>&nbsp; &nbsp;</h5>

            <p class="mb-5">
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出国留学、培训深造、“学人之长补己之短”，是众多莘莘学子们梦寐以求的心愿。中国教育部也保持一贯支持的方针政策，明确“支持留学、鼓励回国、来去自由、发挥作用”。
            </p>
            <p class="mb-5">
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;新辉煌国际教育积极顺应社会及市场发展需求，热诚关注每一位学子的渴求，竭诚为学子们提供完善的留学服务。当下留学环境已发生巨大的变化，我们依托行业前辈十数年的专业经验和资源，提供与时俱进、全面优质的留学项目及服务。
            </p>
          </div>

          <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align:center;">
            <picture>
              <source srcset="图片/新辉煌留学世界.webp" type="image/webp">
              <img src="图片/新辉煌留学世界.png" class="img-fluid" alt=""
                style="max-width:100%;height:auto;margin:0 auto;display:block;" />
            </picture>
          </div>
        </div>
      </div>
    </div>

  <!-- ========== 底部栏区块 ========== -->
  <div id="footer-placeholder"></div>
  <script>
    fetch('footer.html')
      .then(res => res.text())
      .then(html => { document.getElementById('footer-placeholder').innerHTML = html; });
  </script>
  <!-- 底部栏区块结束 -->

    <!-- 右侧悬浮客服区块 -->
    <div id="custom-service-placeholder"></div>
    <script>
      fetch('components/custom-service.html')
        .then(res => res.text())
        .then(html => {
          document.getElementById('custom-service-placeholder').innerHTML = html;
        });
    </script>
    <!-- 右侧悬浮客服区块结束 -->

    <!-- ========== 本地JS依赖区块 ========== -->
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/counterup/counterup.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="js/main.js"></script>
    <!-- ========== 本地JS依赖区块 End ========== -->
  </body>
</html>

