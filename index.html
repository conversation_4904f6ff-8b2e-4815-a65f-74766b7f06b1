<!-- ========== 新辉煌出国 官网首页 index.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <!-- ========== 头部meta与样式引入区块 ========== -->
    <meta charset="utf-8" />
    <title>新辉煌出国</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="" name="keywords" />
    <meta content="" name="description" />
    <link href="css/responsive.css" rel="stylesheet" />
    <link rel="stylesheet" href="css/fontawesome/all.css" />
    <link href="css/bootstrap-icons/bootstrap-icons.css" rel="stylesheet" />
    <link href="lib/animate/animate.min.css" rel="stylesheet" />
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />
    <link href="css/bootstrap.min.css" rel="stylesheet" />
    <link href="css/style.css" rel="stylesheet" />
    <link href="css/css4/gg1.css" rel="stylesheet" />
    <style>
      .style2 {
        display: none;
        /* Hide all elements initially */
      }

      .hover:hover {
        color: var(--bs-secondary) !important;
      }
    </style>
    <link rel="shortcut icon" href="图片/新辉煌logo.png" />
    <!-- ========== 头部meta与样式引入区块 End ========== -->
  </head>

  <body>
    <!-- ========== 顶部加载动画区块 ========== -->
    <!-- Spinner加载动画 -->
    <div
      id="spinner"
      class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center"
    >
      <div
        class="spinner-border text-secondary"
        style="width: 109.5px; height: 109.5px"
        role="status"
      >
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <!-- Spinner End -->
    <!-- ========== 顶部加载动画区块 End ========== -->

    <!-- ========== 顶部栏区块 ========== -->
    <div class="container-fluid bg-primary px-5 d-none d-lg-block">
      <div class="row gx-0 align-items-center">
        <div class="col-lg-5 text-center text-lg-start mb-lg-0">
          <div class="d-flex"></div>
        </div>
        <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
          <div
            class="d-inline-flex align-items-center"
            style="height: 1.2329rem"
          ></div>
        </div>
        <div class="col-lg-4 text-center text-lg-end">
          <div
            class="d-inline-flex align-items-center"
            style="height: 1.2329rem"
          ></div>
        </div>
      </div>
    </div>
    <!-- ========== 顶部栏区块 End ========== -->

    <!-- ========== 导航栏区块 ========== -->
    <div class="container-fluid nav-bar p-0">
      <nav
        class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0"
      >
        <a href="" class="navbar-brand p-0">
          <h1 class="display-5 text-secondary m-0">
            <picture>
              <source srcset="图片/logo.webp" type="image/webp" />
              <img
                src="图片/logo.png"
                class="img-fluid"
                alt=""
                loading="lazy"
                style="max-height: 2.1918rem"
              />
            </picture>
          </h1>
          <!-- <img src="img/logo.png" alt="Logo"> -->
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarCollapse"
        >
          <span class="fa fa-bars"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
          <div class="navbar-nav ms-auto py-0">
            <a href="index.html" class="nav-item nav-link active">首页</a>
            <a href="about.html" class="nav-item nav-link">关于我们</a>
            <div class="nav-item dropdown">
              <a
                href="Immigration.html"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='Immigration.html'"
              >
                <span class="dropdown-toggle">移民项目</span>
              </a>
              <div class="dropdown-menu m-0">
                <a href=" Immigration-USA.html" class="dropdown-item">美国</a>
                <a href="Immigration-Canada.html" class="dropdown-item">加拿大</a>
                <a href="Immigration-Ireland.html" class="dropdown-item">爱尔兰</a>
                <a href="Immigration-HongKong.html" class="dropdown-item">香港</a>
                <a href="Immigration-SingaporeEP.html" class="dropdown-item"
                  >新加坡</a
                >
                <a href="Immigration-Grenada.html" class="dropdown-item"
                  >格林纳达</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#jiaoyu'"
                ><span class="dropdown-toggle">国际教育</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item"
                  >留学申请</a
                >
                <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item"
                  >线上课程</a
                >
                <a href="InternationalEducation-TopSchools.html" class="dropdown-item"
                  >名校直通车计划</a
                >
                <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item"
                  >语言课程</a
                >
                <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item"
                  >游学、特色团</a
                >
                <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item"
                  >国际学校</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#haiwai'"
                ><span class="dropdown-toggle">海外置业</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
                <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
                <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
                <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#pingtai'"
                ><span class="dropdown-toggle">平台合作</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item"
                  >合和法律平台</a
                >
                <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item"
                  >精英留学平台</a
                >
                <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item"
                  >新辉煌金融平台</a
                >
              </div>
            </div>
            <a href="contact.html" class="nav-item nav-link">联系我们</a>
          </div>
        </div>
      </nav>
    </div>
    <!-- ========== 导航栏区块 End ========== -->

    <!-- ========== 轮播图区块 ========== -->
    <div class="carousel-header">
      <div id="carouselId" class="carousel slide" data-bs-ride="carousel">
        <ol class="carousel-indicators">
          <li
            data-bs-target="#carouselId"
            data-bs-slide-to="0"
            class="active"
          ></li>
          <li data-bs-target="#carouselId" data-bs-slide-to="1"></li>
          <li data-bs-target="#carouselId" data-bs-slide-to="2"></li>
          <li data-bs-target="#carouselId" data-bs-slide-to="3"></li>
          <li data-bs-target="#carouselId" data-bs-slide-to="4"></li>
          <li data-bs-target="#carouselId" data-bs-slide-to="5"></li>
        </ol>
        <div class="carousel-inner" role="listbox">
          <div class="carousel-item active">
            <picture>
              <source srcset="img/carousel-1.webp" type="image/webp" />
              <img
                src="img/carousel-1.jpg"
                class="img-fluid"
                alt="Image"
                loading="lazy"
              />
            </picture>
            <div
              class="carousel-caption"
              style="
                background: linear-gradient(
                  rgba(0, 0, 0, 0.6),
                  rgba(0, 0, 0, 0.6)
                );
              "
            >
              <div class="text-center p-4 index-hero-title" style="max-width: 24.6849rem">
                <h4
                  class="text-white text-uppercase fw-bold mb-3 mb-md-4 wow fadeInUp"
                  data-wow-delay="0s"
                >
                  各类签证的解决方案
                </h4>
                <h1
                  class="display-1 text-capitalize text-white mb-3 mb-md-4 wow fadeInUp"
                  data-wow-delay="0s"
                >
                  海外生活从这里开始！
                </h1>
                <p
                  class="text-white mb-4 mb-md-5 fs-5 wow fadeInUp"
                  data-wow-delay="0s"
                >
                  移民规划、子女教育、安家置业、投资出海。<br />
                  我们为您提供全面的海外生活方式，中外直营。
                </p>
                <a
                  class="btn btn-primary border-secondary rounded-pill text-white py-3 px-5 wow fadeInUp"
                  data-wow-delay="0.7s"
                  href="#"
                  >查看所有清单</a
                >
              </div>
            </div>
          </div>

          <div class="carousel-item">
            <picture>
              <source srcset="图片/头图-美国.webp" type="image/webp" />
              <img src="图片/头图-美国.jpg" class="img-fluid" alt="Image" />
            </picture>
            <a href=" Immigration-USA.html">
              <div
                class="carousel-caption"
                style="
                  background: linear-gradient(
                    rgba(0, 0, 0, 0),
                    rgba(0, 0, 0, 0)
                  );
                "
              ></div>
            </a>
          </div>

          <div class="carousel-item">
            <picture>
              <source srcset="图片/头图-爱尔兰.webp" type="image/webp" />
              <img src="图片/头图-爱尔兰.jpg" class="img-fluid" alt="Image" />
            </picture>
            <a href="Immigration-Ireland.html">
              <div
                class="carousel-caption"
                style="
                  background: linear-gradient(
                    rgba(0, 0, 0, 0),
                    rgba(0, 0, 0, 0)
                  );
                "
              ></div>
            </a>
          </div>

          <div class="carousel-item">
            <picture>
              <source srcset="图片/头图-加拿大.webp" type="image/webp" />
              <img src="图片/头图-加拿大.jpg" class="img-fluid" alt="Image" />
            </picture>
            <a href="Immigration-Canada.html">
              <div
                class="carousel-caption"
                style="
                  background: linear-gradient(
                    rgba(0, 0, 0, 0),
                    rgba(0, 0, 0, 0)
                  );
                "
              ></div>
            </a>
          </div>

          <div class="carousel-item">
            <picture>
              <source srcset="图片/头图-香港.webp" type="image/webp" />
              <img src="图片/头图-香港.jpg" class="img-fluid" alt="Image" />
            </picture>
            <a href="Immigration-HongKong.html">
              <div
                class="carousel-caption"
                style="
                  background: linear-gradient(
                    rgba(0, 0, 0, 0),
                    rgba(0, 0, 0, 0)
                  );
                "
              ></div>
            </a>
          </div>

          <div class="carousel-item">
            <picture>
              <source srcset="图片/头图-lv.webp" type="image/webp" />
              <img src="图片/头图-lv.jpg" class="img-fluid" alt="Image" />
            </picture>
            <a href="https://londonandvictoria.com/">
              <div
                class="carousel-caption"
                style="
                  background: linear-gradient(
                    rgba(0, 0, 0, 0.3),
                    rgba(0, 0, 0, 0.3)
                  );
                "
              >
                <div class="text-center p-4" style="max-width: 24.6575rem">
                  <h4
                    class="text-white text-uppercase fw-bold mb-3 mb-md-4 wow fadeInUp"
                    data-wow-delay="0s"
                  ></h4>
                  <h4
                    class="display-1 text-capitalize text-white mb-3 mb-md-4 wow fadeInUp"
                    data-wow-delay="0s"
                    style="font-size: 164.25px"
                  >
                    与海房买家分享我们深厚的海外置业知识
                  </h4>
                  <p
                    class="text-white mb-4 mb-md-5 fs-5 wow fadeInUp"
                    data-wow-delay="0s"
                  ></p>
                  <a
                    class="btn btn-primary border-secondary rounded-pill text-white py-3 px-5 wow fadeInUp"
                    data-wow-delay="0.7s"
                    href="http://www.londonandvictoria.com"
                    >了解更多</a
                  >
                </div>
              </div>
            </a>
          </div>
        </div>
        <button
          class="carousel-control-prev"
          type="button"
          data-bs-target="#carouselId"
          data-bs-slide="prev"
        >
          <span
            style="margin-top: 0rem"
            class="carousel-control-prev-icon bg-secondary wow fadeInLeft"
            data-wow-delay="0.2s"
            aria-hidden="false"
          ></span>
          <span class="visually-hidden-focusable">Previous</span>
        </button>
        <button
          class="carousel-control-next"
          type="button"
          data-bs-target="#carouselId"
          data-bs-slide="next"
        >
          <span
            style="margin-top: 0rem"
            class="carousel-control-next-icon bg-secondary wow fadeInRight"
            data-wow-delay="0.2s"
            aria-hidden="false"
          ></span>
          <span class="visually-hidden-focusable">Next</span>
        </button>
      </div>
    </div>
    <!-- ========== 轮播图区块 End ========== -->

    <!-- ========== 搜索弹窗区块 ========== -->
    <div
      class="modal fade"
      id="searchModal"
      tabindex="-1"
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content rounded-0">
          <div class="modal-header">
            <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">
              搜索
            </h4>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body d-flex align-items-center">
            <div class="input-group w-75 mx-auto d-flex">
              <input
                type="search"
                class="form-control p-3"
                placeholder="输入关键词搜索"
                aria-describedby="search-icon-1"
              />
              <span id="search-icon-1" class="input-group-text p-3"
                ><i class="fa fa-search"></i
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 搜索弹窗区块 End ========== -->

    <!-- ========== 关于我们区块 ========== -->
    <div class="container-fluid py-5">
      <div class="container py-5">
        <div class="row g-5">
          <div class="col-xl-5 wow fadeInLeft" data-wow-delay="0.1s">
            <div class="bg-light rounded">
              <picture>
                <source srcset="img/about-2.webp" type="image/webp" />
                <img
                  src="img/about-2.png"
                  class="img-fluid w-100"
                  style="margin-bottom: -0.1918rem"
                  alt="Image"
                  loading="lazy"
                />
              </picture>
              <picture>
                <source srcset="img/about-3.webp" type="image/webp" />
                <img
                  src="img/about-3.jpg"
                  class="img-fluid w-100 border-bottom border-5 border-primary"
                  style="
                    border-top-right-radius: 8.2192rem;
                    border-top-left-radius: 8.2192rem;
                  "
                  alt="Image"
                  loading="lazy"
                />
              </picture>
            </div>
          </div>
          <div class="col-xl-7 wow fadeInRight" data-wow-delay="0.3s">
            <h5 class="sub-title pe-3">关于公司</h5>
            <h1 class="display-5 mb-4">
              我们是领先的 B to B 联合出国项目方公司
            </h1>
            <p class="mb-4">
              <b>"出国找辉煌，一站通全球"。</b
              >新辉煌出国一直致力于为中国高净值人士提供一站式海外需求解决方案。包括海外身份规划、子女国际教育、海外安家置业、等跨境服务的源头资源。
            </p>
            <div class="row gy-4 align-items-center">
              <div class="col-12 col-sm-6 d-flex align-items-center">
                <i class="fas fa-passport fa-3x text-secondary"></i>
                <h5 class="ms-4">优质海外源头资源</h5>
              </div>

              <div class="col-12 col-sm-6 d-flex align-items-center">
                <i class="fas fa-shield-alt fa-3x text-secondary"></i>
                <h5 class="ms-4">中外直营全面护航</h5>
              </div>

              <div class="col-4 col-md-3">
                <div class="bg-light text-center rounded p-3">
                  <div class="mb-2"></div>
                  <h1 class="display-5 fw-bold mb-2 text-secondary2">49年</h1>
                  <p class="text-muted mb-0">行业经验</p>
                </div>
              </div>
              <div class="col-8 col-md-9">
                <div class="mb-5">
                  <p class="text-primary h6 mb-3">
                    <i class="fa fa-check-circle text-secondary me-2"></i>
                    多元服务：移民、留学、海外置业，全面提供
                  </p>
                  <p class="text-primary h6 mb-3">
                    <i class="fa fa-check-circle text-secondary me-2"></i>
                    源头项目：海外源头项目资源，深度溯源、更高佣金
                  </p>
                  <p class="text-primary h6 mb-3">
                    <i class="fa fa-check-circle text-secondary me-2"></i>
                    安全保障：移民项目可提供资金监管、法律护航
                  </p>
                </div>
                <div class="d-flex flex-wrap">
                  <div
                    id="phone-tada"
                    class="d-flex align-items-center justify-content-center me-4"
                  >
                    <a
                      href=""
                      class="position-relative wow tada"
                      data-wow-delay=".9s"
                    >
                      <i class="fab fa-weixin text-primary fa-3x"></i>
                    </a>
                  </div>

                  <div class="d-flex flex-column justify-content-center">
                    <span class="text-primary">有任何问题吗？</span>
                    <span
                      class="text-secondary fw-bold fs-5"
                      style="letter-spacing: .0548rem"
                      >欢迎合作咨询：</span
                    >
                  </div>

                  <div class="d-flex flex-column justify-content-center">
                    <picture>
                      <source srcset="图片/二维码.webp" type="image/webp" />
                      <img
                        src="图片/二维码.jpg"
                        class=""
                        alt=""
                        style="max-height: 2.1918rem"
                      />
                    </picture>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 关于我们区块 End ========== -->

    <!-- ========== 核心优势区块 ========== -->
    <div class="container-fluid counter-facts py-5">
      <!-- Carousel Start -->
      <div
        style="
          display: flex;
          flex-wrap: nowrap;
          flex-direction: row;
          justify-content: space-evenly;
        "
      >
        <div
          id="carouselId1"
          class="carousel slide img_index"
          data-bs-ride="carousel"
          data-bs-pause="false"
          data-bs-interval="2000"
          style="width: 20%"
        >
          <div
            class="carousel-inner"
            role="listbox1"
            style="border-radius: .8219rem"
          >
            <div class="carousel-item active">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-1.webp" type="image/webp" />
                  <img src="图片/滚动/2-1.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
            <div class="carousel-item">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-2.webp" type="image/webp" />
                  <img src="图片/滚动/2-2.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
            <div class="carousel-item">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-3.webp" type="image/webp" />
                  <img src="图片/滚动/2-3.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
          </div>
        </div>
        <div
          id="carouselId2"
          class="carousel slide img_index"
          data-bs-ride="carousel"
          data-bs-pause="false"
          data-bs-interval="2000"
          style="width: 20%"
        >
          <div
            class="carousel-inner"
            role="listbox1"
            style="border-radius: .8219rem"
          >
            <div class="carousel-item active">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-1.webp" type="image/webp" />
                  <img src="图片/滚动/2-1.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
            <div class="carousel-item">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-2.webp" type="image/webp" />
                  <img src="图片/滚动/2-2.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
            <div class="carousel-item">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-3.webp" type="image/webp" />
                  <img src="图片/滚动/2-3.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
          </div>
        </div>
        <div
          id="carouselId3"
          class="carousel slide img_index"
          data-bs-ride="carousel"
          data-bs-pause="false"
          data-bs-interval="2000"
          style="width: 20%"
        >
          <div
            class="carousel-inner"
            role="listbox1"
            style="border-radius: .8219rem"
          >
            <div class="carousel-item active">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-1.webp" type="image/webp" />
                  <img src="图片/滚动/2-1.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
            <div class="carousel-item">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-2.webp" type="image/webp" />
                  <img src="图片/滚动/2-2.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
            <div class="carousel-item">
              <a>
                <picture>
                  <source srcset="图片/滚动/2-3.webp" type="image/webp" />
                  <img src="图片/滚动/2-3.jpg" class="img-fluid" alt="Image" />
                </picture>
              </a>
            </div>
          </div>
        </div>
      </div>
      <div id="counter-features-container"></div>
      <script>
        fetch("components/counter-features.html")
          .then((res) => res.text())
          .then((html) => {
            document.getElementById("counter-features-container").innerHTML =
              html;
          });
      </script>
    </div>
    <!-- ========== 核心优势区块 End ========== -->

    <!-- ========== 移民规划区块 ========== -->
    <div class="container-fluid country overflow-hidden py-5" id="yimin">
      <div class="container">
        <div
          class="section-title text-center wow fadeInUp"
          data-wow-delay="0.1s"
          style="margin-bottom: 1.9178rem"
        >
          <div class="sub-style">
            <h5 class="sub-title text-primary px-3">移民规划</h5>
          </div>
          <h1 class="display-5 mb-4">
            我们提供以下国家和地区的<br />移民和签证服务
          </h1>
          <p class="mb-0">
            新辉煌出国提供海外源头资源的移民项目，多为中外直营。<br />
            所有投资项目均可实地考察，真实透明！
          </p>
        </div>
        <div class="row g-4 text-center">
          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 70%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  香港
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/香港.webp" type="image/webp" />
                  <img
                    src="图片/香港.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/香港旗帜.webp" type="image/webp" />
                  <img
                    src="图片/香港旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>

              <div class="country-name" style="text-align: justify; width: 80%">
                <a style="color: #ffffff" href="Immigration-HongKong.html">
                  <h2
                    align="center"
                    class=""
                    style="color: #ffffff; font-weight: bold"
                  >
                    <br />香港<br />
                  </h2>
                  <b>新资本投资者入境计划（NEW CIES）：</b
                  >"王者项目"时隔8年再度重启；投资3000万港币，精心打造保底投资方案供您选择。
                  <br /><a
                    class="hover"
                    style="color: #ffffff"
                    href="Immigration-HongKong.html"
                    >了解更多→</a
                  >
                </a>
              </div>
            </div>
          </div>

          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.1s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 70%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  美国
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/美国.webp" type="image/webp" />
                  <img
                    src="图片/美国.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/美国旗帜.webp" type="image/webp" />
                  <img
                    src="图片/美国旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a href=" Immigration-USA.html" class="" style="color: #ffffff">
                  <h2 align="center" style="color: #ffffff; font-weight: bold">
                    <br />美国<br />
                  </h2>
                  <b>EB-5直投：</b
                  >无语言、无排期、90天快速获批"小绿卡"；绿卡保障、商业透明、绝无"区域中心诈骗"风险，最快2年退出。
                  <br /><a
                    class="hover"
                    style="color: #ffffff"
                    href=" Immigration-USA.html"
                    >了解更多→</a
                  >
                </a>
              </div>
            </div>
          </div>

          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.3s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 80%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  加拿大
                </h2>
              </div>
              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/加拿大.webp" type="image/webp" />
                  <img
                    src="图片/加拿大.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/加拿大旗帜.webp" type="image/webp" />
                  <img
                    src="图片/加拿大旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a href="Immigration-Canada.html" class="" style="color: #ffffff">
                  <h2 align="center" style="color: #ffffff; font-weight: bold">
                    <br />加拿大<br />
                  </h2>
                  <b>联邦创投（SUV）：</b
                  >联邦项目，仅需雅思5分，全家一步到位拿枫叶卡；国家级主创项目，国内多基地考察，投资款有返还，费用更低。。
                  <br /><a
                    href="Immigration-Canada.html"
                    class="hover"
                    style="color: #ffffff"
                    >了解更多→</a
                  >
                </a>
              </div>
            </div>
          </div>
          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.5s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 80%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  爱尔兰
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/爱尔兰.webp" type="image/webp" />
                  <img
                    src="图片/爱尔兰.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/爱尔兰旗帜.webp" type="image/webp" />
                  <img
                    src="图片/爱尔兰旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a href="Immigration-Ireland.html" class="" style="color: #ffffff">
                  <h2 align="center" style="color: #ffffff; font-weight: bold">
                    <br />爱尔兰<br />
                  </h2>
                  <b>高技能工作居留：</b
                  >无需雅思，快至2个月获批，21个月转永居；爱尔兰本土集团化公司源头雇主直聘，免费安家服务大礼包，不成功全退费。
                  <br /><a
                    href="Immigration-Ireland.html"
                    class="hover"
                    style="color: #ffffff"
                    >了解更多→</a
                  >
                </a>
              </div>
            </div>
          </div>
          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.7s"
            style="width: 20%"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  margin: 0 0 0 0;
                  width: 90%;
                  position: absolute;
                  top: 40%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  第三国身份
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/格林纳达.webp" type="image/webp" />
                  <img
                    src="图片/格林纳达.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/格林纳达旗帜.webp" type="image/webp" />
                  <img
                    src="图片/格林纳达旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a
                  href="Immigration-Grenada.html"
                  class=""
                  style="color: #ffffff"
                >
                  <h2 align="center" style="color: #ffffff; font-weight: bold">
                    <br />第三国身份<br />
                  </h2>
                  格林纳达护照——英联邦护照、"美国后花园"、美元资产配置身份首选、世界知名医学院可英/美/加执业、E2签证快速登陆美国、与中国互免。
                  <br /><a
                    href="Immigration-Grenada.html"
                    class="hover"
                    style="color: #ffffff"
                    >了解更多→</a
                  >
                </a>
              </div>
            </div>
          </div>

          <div class="col-12">
            <a
              class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
              data-wow-delay="0.1s"
              href="Immigration-SingaporeEP.html"
              >更多国家</a
            >
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 移民规划区块 End ========== -->

    <!-- ========== 国际教育区块 ========== -->
    <div class="container-fluid service overflow-hidden pt-5" id="jiaoyu">
      <div class="container py-5">
        <div
          class="section-title text-center mb-5 wow fadeInUp"
          data-wow-delay="0.1s"
        >
          <div class="sub-style">
            <h5 class="sub-title text-primary px-3">国际教育</h5>
          </div>
          <h1 class="display-5 mb-4">我们提供全面的国际教育精品课程和服务</h1>
          <p class="mb-0">
            新辉煌国际教育在全球范围甄选不同年龄阶段更适合中国孩子的国际课程，<br />
            并立足中国国情研发多种培训课程，助力中国学子抢跑优质国际教育赛道。
          </p>
        </div>
        <div class="row g-4" style="justify-content: space-evenly">
          <div
            class="col-lg-6 col-xl-4 wow fadeInUp"
            data-wow-delay="0.1s"
            style="width: 26%"
          >
            <div class="service-item">
              <div class="service-inner">
                <div class="service-img">
                  <picture>
                    <source srcset="图片/留学申请.webp" type="image/webp" />
                    <img
                      src="图片/留学申请.jpg"
                      class="img-fluid w-100 rounded"
                      alt="Image"
                    />
                  </picture>
                </div>
                <div class="service-title">
                  <div class="service-title-name">
                    <div class="bg-primary text-center rounded p-3 mx-5 mb-4">
                      <a
                        href="InternationalEducation-StudyAbroadApplication.html"
                        class="h4 text-white mb-0"
                        >留学申请</a
                      >
                    </div>
                  </div>
                  <div class="service-content pb-4">
                    <a href="InternationalEducation-StudyAbroadApplication.html">
                      <h4 class="text-white mb-4 py-3">留学申请</h4>
                    </a>
                    <div class="px-4">
                      <a href="InternationalEducation-StudyAbroadApplication.html" style="color: #7a8a9e">
                        <p class="mb-4">
                          专长北美和英联邦国家的一站式留学申请及留学相关服务。
                        </p>
                      </a>
                      <a
                        class="hover"
                        style="color: #ffffff"
                        href="InternationalEducation-StudyAbroadApplication.html"
                        >了解更多→</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="col-lg-6 col-xl-4 wow fadeInUp"
            data-wow-delay="0.3s"
            style="width: 26%"
          >
            <div class="service-item">
              <div class="service-inner">
                <div class="service-img">
                  <picture>
                    <source srcset="图片/线上课程.webp" type="image/webp" />
                    <img
                      src="图片/线上课程.jpg"
                      class="img-fluid w-100 rounded"
                      alt="Image"
                    />
                  </picture>
                </div>
                <div class="service-title">
                  <div class="service-title-name">
                    <div class="bg-primary text-center rounded p-3 mx-5 mb-4">
                      <a
                        href="InternationalEducation-OnlineCourses.html "
                        class="h4 text-white mb-0"
                        >线上课程</a
                      >
                    </div>
                  </div>
                  <div class="service-content pb-4">
                    <a href="InternationalEducation-OnlineCourses.html  ">
                      <h4 class="text-white mb-4 py-3">线上课程</h4>
                    </a>
                    <div class="px-4">
                      <a href="InternationalEducation-OnlineCourses.html" style="color: #7a8a9e">
                        <p class="mb-4">
                          美国线上高中、英国精英私校UKiset/CE考试及面试培训、新加坡政府中小学入学考试培训、港澳台侨联考培训等线上课程
                        </p>
                      </a>
                      <a
                        class="hover"
                        style="color: #ffffff"
                        href="InternationalEducation-OnlineCourses.html  "
                        >了解更多→</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="col-lg-6 col-xl-4 wow fadeInUp"
            data-wow-delay="0.5s"
            style="width: 26%"
          >
            <div class="service-item">
              <div class="service-inner">
                <div class="service-img">
                  <picture>
                    <source srcset="图片/名校计划.webp" type="image/webp" />
                    <img
                      src="图片/名校计划.jpg"
                      class="img-fluid w-100 rounded"
                      alt="Image"
                    />
                  </picture>
                </div>
                <div class="service-title">
                  <div class="service-title-name">
                    <div class="bg-primary text-center rounded p-3 mx-5 mb-4">
                      <a
                        href="InternationalEducation-TopSchools.html"
                        class="h4 text-white mb-0"
                        >名校直通车计划</a
                      >
                    </div>
                  </div>
                  <div class="service-content pb-4">
                    <a href="InternationalEducation-TopSchools.html">
                      <h4 class="text-white mb-4 py-3">名校直通车计划</h4>
                    </a>
                    <div class="px-4">
                      <a href="InternationalEducation-TopSchools.html" style="color: #7a8a9e">
                        <p class="mb-4">
                          北美名校Top100本科及研究生直升、北英名校本科及研究生直升、世界知名医学院申请等。
                        </p>
                      </a>
                      <a
                        class="hover"
                        style="color: #ffffff"
                        href="InternationalEducation-TopSchools.html"
                        >了解更多→</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            class="col-lg-6 col-xl-4 wow fadeInUp"
            data-wow-delay="0.1s"
            style="width: 26%"
          >
            <div class="service-item">
              <div class="service-inner">
                <div class="service-img">
                  <picture>
                    <source srcset="图片/语言课程.webp" type="image/webp" />
                    <img
                      src="图片/语言课程.jpg"
                      class="img-fluid w-100 rounded"
                      alt="Image"
                    />
                  </picture>
                </div>
                <div class="service-title">
                  <div class="service-title-name">
                    <div class="bg-primary text-center rounded p-3 mx-5 mb-4">
                      <a
                        href="InternationalEducation-LanguageCourses.html"
                        class="h4 text-white mb-0"
                        >语言课程</a
                      >
                    </div>
                  </div>
                  <div class="service-content pb-4">
                    <a href="InternationalEducation-LanguageCourses.html">
                      <h4 class="text-white mb-4 py-3">语言课程</h4>
                    </a>
                    <div class="px-4">
                      <a href="InternationalEducation-LanguageCourses.html" style="color: #7a8a9e">
                        <p class="mb-4">
                          为出国留学、移民定居、度假、商务的中国人士定制"九大出国英语课程体系"。
                        </p>
                      </a>
                      <a
                        class="hover"
                        style="color: #ffffff"
                        href="InternationalEducation-LanguageCourses.html"
                        >了解更多→</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            class="col-lg-6 col-xl-4 wow fadeInUp"
            data-wow-delay="0.3s"
            style="width: 26%"
          >
            <div class="service-item">
              <div class="service-inner">
                <div class="service-img">
                  <picture>
                    <source srcset="图片/游学、特色团.webp" type="image/webp" />
                    <img
                      src="图片/游学、特色团.jpg"
                      class="img-fluid w-100 rounded"
                      alt="Image"
                    />
                  </picture>
                </div>
                <div class="service-title">
                  <div class="service-title-name">
                    <div class="bg-primary text-center rounded p-3 mx-5 mb-4">
                      <a
                        href="InternationalEducation-StudyTour-SpecialGroups.html"
                        class="h4 text-white mb-0"
                        >游学、特色团</a
                      >
                    </div>
                  </div>
                  <div class="service-content pb-4">
                    <a href="InternationalEducation-StudyTour-SpecialGroups.html">
                      <h4 class="text-white mb-4 py-3">游学、特色团</h4>
                    </a>
                    <div class="px-4" align="center">
                      <a
                        href="InternationalEducation-StudyTour-SpecialGroups.html"
                        style="color: #7a8a9e"
                      >
                        <p class="mb-4" align="center">
                          全球名校游学项目，及各类国际化特色培训团。
                        </p>
                      </a>
                      <a
                        class="hover"
                        style="color: #ffffff"
                        href="InternationalEducation-StudyTour-SpecialGroups.html"
                        >了解更多→</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div
            class="col-lg-6 col-xl-4 wow fadeInUp"
            data-wow-delay="0.3s"
            style="width: 26%"
          >
            <div class="service-item">
              <div class="service-inner">
                <div class="service-img">
                  <picture>
                    <source srcset="图片/国际学校.webp" type="image/webp" />
                    <img
                      src="图片/国际学校.jpg"
                      class="img-fluid w-100 rounded"
                      alt="Image"
                    />
                  </picture>
                </div>
                <div class="service-title">
                  <div class="service-title-name">
                    <div class="bg-primary text-center rounded p-3 mx-5 mb-4">
                      <a
                        href="InternationalEducation-InternationalSchools.html"
                        class="h4 text-white mb-0"
                        >国际学校</a
                      >
                    </div>
                  </div>
                  <div class="service-content pb-4">
                    <a href="InternationalEducation-InternationalSchools.html">
                      <h4 class="text-white mb-4 py-3">国际学校</h4>
                    </a>
                    <div class="px-4">
                      <a href="InternationalEducation-InternationalSchools.html" style="color: #7a8a9e">
                        <p class="mb-4">国内及海外国际学校入学及转学服务。</p>
                      </a>
                      <a
                        class="hover"
                        style="color: #ffffff"
                        href="InternationalEducation-InternationalSchools.html"
                        >了解更多→</a
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 国际教育区块 End ========== -->

    <!-- ========== 海外置业区块 ========== -->
    <div class="container-fluid country overflow-hidden py-5" id="haiwai">
      <div class="container">
        <div
          class="section-title text-center wow fadeInUp"
          data-wow-delay="0.1s"
          style="margin-bottom: 1.9178rem"
        >
          <div class="sub-style">
            <h5 class="sub-title text-primary px-3">海外置业</h5>
          </div>
          <h1 class="display-5 mb-4">
            我们提供以下国家和地区的"家"<br />和投资物业
          </h1>
          <p class="mb-0">
            L&V是一家专业的海外房产经纪公司，起源于香港。<br />
            资深专注于英国、迪拜、香港3000 万投资移民房产投资等海外房产项目。
          </p>
        </div>
        <div class="row g-4 text-center">
          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.1s"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  position: absolute;
                  top: 34%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  &nbsp;&nbsp;香港&nbsp;&nbsp;
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/香港房产.webp" type="image/webp" />
                  <img
                    src="图片/香港房产.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/香港旗帜.webp" type="image/webp" />
                  <img
                    src="图片/香港旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>

              <div class="country-name" style="text-align: justify; width: 80%">
                <a style="color: #ffffff" href="OverseasProperty-HongKong.html">
                  <h2
                    align="center"
                    class="hover"
                    style="color: #ffffff; font-weight: bold"
                  >
                    香港<br />
                  </h2>
                </a>
              </div>
            </div>
          </div>

          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.5s"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  position: absolute;
                  top: 34%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  &nbsp;&nbsp;迪拜&nbsp;&nbsp;
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/迪拜房产.webp" type="image/webp" />
                  <img
                    src="图片/迪拜房产.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/迪拜旗帜.webp" type="image/webp" />
                  <img
                    src="图片/迪拜旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a style="color: #ffffff" href="OverseasProperty-Dubai.html">
                  <h2
                    align="center"
                    class="hover"
                    style="color: #ffffff; font-weight: bold"
                  >
                    迪拜<br />
                  </h2>
                </a>
              </div>
            </div>
          </div>

          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="0.7s"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  position: absolute;
                  top: 34%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  &nbsp;&nbsp;英国&nbsp;&nbsp;
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/英国房产.webp" type="image/webp" />
                  <img
                    src="图片/英国房产.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/英国旗帜.webp" type="image/webp" />
                  <img
                    src="图片/英国旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a style="color: #ffffff" href="OverseasProperty-UK.html">
                  <h2
                    align="center"
                    class="hover"
                    style="color: #ffffff; font-weight: bold"
                  >
                    英国<br />
                  </h2>
                </a>
              </div>
            </div>
          </div>
          <div
            class="col-lg-6 col-xl-3 mb-5 mb-xl-0 wow fadeInUp"
            data-wow-delay="1.0s"
          >
            <div class="country-item">
              <div
                class=" "
                style="
                  position: absolute;
                  top: 34%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                "
              >
                <h2
                  align="center"
                  style="
                    color: #ffffff;
                    font-weight: bold;
                    background: rgba(0, 58, 102, 0.8);
                  "
                >
                  &nbsp;&nbsp;爱尔兰&nbsp;&nbsp;
                </h2>
              </div>

              <div class="rounded overflow-hidden">
                <picture>
                  <source srcset="图片/爱尔兰房产.webp" type="image/webp" />
                  <img
                    src="图片/爱尔兰房产.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-flag">
                <picture>
                  <source srcset="图片/爱尔兰旗帜.webp" type="image/webp" />
                  <img
                    src="图片/爱尔兰旗帜.jpg"
                    class="img-fluid rounded-circle"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="country-name" style="text-align: justify; width: 80%">
                <a style="color: #ffffff" href="OverseasProperty-Ireland.html">
                  <h2
                    align="center"
                    class="hover"
                    style="color: #ffffff; font-weight: bold"
                  >
                    爱尔兰<br />
                  </h2>
                </a>
              </div>
            </div>
          </div>
          <div class="col-12">
            <a
              class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
              data-wow-delay="0.1s"
              href="https://londonandvictoria.com/"
              >更多国家</a
            >
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 海外置业区块 End ========== -->

    <!-- ========== 平台合作区块 ========== -->
    <div
      class="container-fluid training overflow-hidden bg-light py-5"
      id="pingtai"
    >
      <div class="container py-5">
        <div
          class="section-title text-center mb-5 wow fadeInUp"
          data-wow-delay="0.1s"
        >
          <div class="sub-style">
            <h5 class="sub-title text-primary px-3">平台合作</h5>
          </div>
          <h1 class="display-5 mb-4">
            如果您是法律人士、教师、金融人士<br />
            欢迎加入我们
          </h1>

          <p class="mb-0">
            <b>"开源合作、优势赋能"</b><br />
            以新辉煌出国优势海外资源为抓手，为中国高净值人士提供<br />
            全球身份规划、子女国际教育、海外安家置业等跨境咨询服务。
          </p>
        </div>
        <div class="row g-4" style="justify-content: center">
          <div class="col-lg-6 col-xl-3 wow fadeInUp" data-wow-delay="0.1s">
            <div class="training-item">
              <div class="training-inner">
                <picture>
                  <source srcset="图片/合和法律平台.webp" type="image/webp" />
                  <img
                    src="图片/合和法律平台.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="training-content bg-secondary rounded-bottom p-4">
                <a href="PlatformCooperation-HeheAbroad.html">
                  <h4 class="text-white">合和出国平台</h4>

                  <p class="text-white-50">
                    行家眼光·专家服务·法律保障<br />
                    为高净值人士提供全面优质的出国资讯及服务
                  </p>
                </a>
                <a
                  class="hover"
                  style="color: #ffffff"
                  href="PlatformCooperation-HeheAbroad.html"
                  >了解更多 <i class="fa fa-arrow-right"></i
                ></a>
              </div>
            </div>
          </div>

          <div class="col-lg-6 col-xl-3 wow fadeInUp" data-wow-delay="0.3s">
            <div class="training-item">
              <div class="training-inner">
                <picture>
                  <source srcset="图片/精英留学平台.webp" type="image/webp" />
                  <img
                    src="图片/精英留学平台.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="training-content bg-secondary rounded-bottom p-4">
                <a href="PlatformCooperation-EliteStudyAbroad.html">
                  <h4 class="text-white">精英留学平台</h4>
                  <p class="text-white-50">
                    50年行业经验·海内外名校资源·超专业服务团队<br />
                    助力中国学子留学强国之路
                  </p>
                </a>
                <a
                  class="hover"
                  style="color: #ffffff"
                  href="PlatformCooperation-EliteStudyAbroad.html"
                  >了解更多 <i class="fa fa-arrow-right"></i
                ></a>
              </div>
            </div>
          </div>

          <div class="col-lg-6 col-xl-3 wow fadeInUp" data-wow-delay="0.7s">
            <div class="training-item">
              <div class="training-inner">
                <picture>
                  <source srcset="图片/新辉煌金融平台.webp" type="image/webp" />
                  <img
                    src="图片/新辉煌金融平台.jpg"
                    class="img-fluid w-100 rounded"
                    alt="Image"
                  />
                </picture>
              </div>
              <div class="training-content bg-secondary rounded-bottom p-4">
                <a href="PlatformCooperation-NewGloryFinance.html">
                  <h4 class="text-white">新辉煌金融平台</h4>
                  <p class="text-white-50">
                    秉承"专业、卓越、务实"的服务理念<br />
                    为高净值人士提供优质的海外专业资讯服务
                  </p>
                </a>
                <a
                  class="hover"
                  style="color: #ffffff"
                  href="PlatformCooperation-NewGloryFinance.html"
                  >了解更多 <i class="fa fa-arrow-right"></i
                ></a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 平台合作区块 End ========== -->

    <!-- ========== 联系我们区块 ========== -->
    <div class="container-fluid contact overflow-hidden py-5">
      <div class="container py-5">
        <div class="row g-5 mb-5">
          <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3">
            <picture>
              <source srcset="图片/广州.webp" type="image/webp" />
              <img
                src="图片/广州.jpg"
                class="img-fluid"
                alt=""
                style="max-width: 100%; margin-top: 0%"
              />
            </picture>
          </div>

          <div
            class="col-lg-6 wow fadeInLeft"
            data-wow-delay="0.1s"
            style="
              display: flex;
              flex-direction: column;
              justify-content: space-between;
            "
          >
            <div class="sub-style">
              <h5 class="sub-title text-primary pe-3">快速联系</h5>
            </div>
            <h1 class="display-5 mb-4">项目合作欢迎联系</h1>
            <p class="mb-5"></p>

            <div class="row g-3">
              <div class="col-xl-6">
                <div class="d-flex">
                  <i class="fas fa-envelope fa-3x text-primary"></i>
                  <div class="ps-3">
                    <div class="mb-3">
                      <h6 class="mb-0">电子邮件:</h6>
                      <a href="#" class="fs-5 text-primary"><EMAIL></a>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-xl-6">
                <div class="d-flex">
                  <i class="fab fa-weixin fa-3x text-primary"></i>
                  <div class="ps-3">
                    <h5 class="mb-3">扫描微信二维码</h5>
                    <picture>
                      <source srcset="图片/二维码.webp" type="image/webp" />
                      <img
                        src="图片/二维码.jpg"
                        style="max-width: 50%; margin: 0% 0 0 0%"
                      />
                    </picture>
                  </div>
                </div>
              </div>
              <h5 class="text-primary pe-3 bottom-line">
                （温馨提示：来信请备注"出国项目合作"。）
              </h5>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ========== 联系我们区块 End ========== -->

    <!-- ========== 底部栏区块 ========== -->
    <div id="footer-placeholder"></div>
    <script>
      fetch("footer.html")
        .then((res) => res.text())
        .then((html) => {
          document.getElementById("footer-placeholder").innerHTML = html;
        });
    </script>
    <!-- ========== 底部栏区块 End ========== -->

    <!-- ========== 悬浮客服区块 ========== -->
    <div id="custom-service-placeholder"></div>
    <script>
      fetch("components/custom-service.html")
        .then((res) => res.text())
        .then((html) => {
          document.getElementById("custom-service-placeholder").innerHTML =
            html;
        });
    </script>
    <!-- ========== 悬浮客服区块 End ========== -->

    <!-- ========== 本地JS依赖区块 ========== -->
    <!-- JavaScript Libraries -->
    <script src="./js/jquery-3.6.4.min.js"></script>
    <script src="./js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/counterup/counterup.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>

    <!-- Template Javascript -->
    <script src="js/main.js"></script>
    <!-- ========== 本地JS依赖区块 End ========== -->
  </body>
</html>

