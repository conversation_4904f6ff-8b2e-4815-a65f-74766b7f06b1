﻿<!-- ========== 新辉煌出国 InternationalEducation-TopSchools.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <!-- ========== 头部meta与样式引入区块 ========== -->
  <meta charset="utf-8" />
  <title>新辉煌出国</title>
  <meta content="width=device-width, initial-scale=1.0" name="viewport" />
  <meta content="" name="keywords" />
  <meta content="" name="description" />
  <link href="css/responsive.css" rel="stylesheet" />
  <!-- 本地图标字体，已本地化 -->
  <link rel="stylesheet" href="css/fontawesome/all.css" />
  <link rel="stylesheet" href="css/bootstrap-icons/bootstrap-icons.css" />

  <!-- 本地动画库、轮播库样式 -->
  <link href="lib/animate/animate.min.css" rel="stylesheet" />
  <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />

  <!-- 结构化区块样式 -->
  <link href="css/css4/gg1.css" rel="stylesheet" />
  <link rel="stylesheet" href="./css/merged/reset1.css" />
  <link rel="stylesheet" href="./css/merged/common1.css" />
  <link rel="stylesheet" href="./css/merged/window1.css" />
  <link rel="stylesheet" href="./css/merged/advisers1.css" />
  <link rel="stylesheet" href="./css/merged/flag_window.css" />
  <link rel="stylesheet" href="./css/merged/flag.css" />
  <link rel="stylesheet" href="./css/merged/swiper.css" />
  <link rel="stylesheet" href="./css/merged/globe_subject.css" />
  <link rel="stylesheet" href="./css/merged/public.css" />
  <link rel="stylesheet" href="./css/merged/index_new.css" />
  <link rel="stylesheet" href="./css/merged/project_page_cq_new.css" />
  <link rel="stylesheet" href="./css/merged/jinrErweima.css" />
  <link rel="stylesheet" href="./css/merged/flag(1).css" />

  <!-- 本地Bootstrap样式 -->
  <link href="css/bootstrap.min.css" rel="stylesheet" />

  <!-- 站点主样式，结构化注释 -->
  <link href="css/style.css" rel="stylesheet" />

  <link rel="shortcut icon" href="图片/新辉煌logo.png" />

  <style>
    /* 隐藏style2样式 */
    .style2 {
      display: none;
    }

    .hover:hover {
      color: var(--bs-secondary) !important;
    }
  </style>
  <!-- ========== 头部meta与样式引入区块 End ========== -->
</head>

<body>
  <!-- ========== 顶部加载动画区块 ========== -->
  <div id="spinner"
    class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
    <div class="spinner-border text-secondary" style="width: 3rem; height: 3rem" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
  <!-- 顶部加载动画区块结束 -->

  <!-- 顶部栏区块 -->
    <div class="container-fluid bg-primary px-5 d-none d-lg-block">
      <div class="row gx-0 align-items-center">
        <div class="col-lg-5 text-center text-lg-start mb-lg-0">
          <div class="d-flex"></div>
        </div>
        <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
        <div class="col-lg-4 text-center text-lg-end">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
      </div>
    </div>
  <!-- 顶部栏区块结束 -->

  <!-- 导航栏区块 -->
  <div class="container-fluid nav-bar p-0">
    <nav class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0">
      <a href="" class="navbar-brand p-0">
        <h1 class="display-5 text-secondary m-0">
          <picture>
            <source srcset="图片/logo.webp" type="image/webp">
            <img src="图片/logo.png" class="img-fluid" alt="" style="max-height: 80px" />
          </picture>
        </h1>
        <!-- <img src="img/logo.png" alt="Logo"> -->
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
        <span class="fa fa-bars"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarCollapse">
        <div class="navbar-nav ms-auto py-0">
          <a href="index.html" class="nav-item nav-link ">首页</a>
          <a href="about.html" class="nav-item nav-link">关于我们</a>
          <div class="nav-item dropdown">
            <a href="Immigration.html" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='Immigration.html'">
              <span class="dropdown-toggle">移民项目</span>
            </a>
            <div class="dropdown-menu m-0">
              <a href=" Immigration-USA.html" class="dropdown-item">美国</a>
              <a href="Immigration-Canada.html" class="dropdown-item">加拿大</a>
              <a href="Immigration-Ireland.html" class="dropdown-item">爱尔兰</a>
              <a href="Immigration-HongKong.html" class="dropdown-item">香港</a>
              <a href="Immigration-SingaporeEP.html" class="dropdown-item">新加坡</a>
              <a href="Immigration-Grenada.html" class="dropdown-item">格林纳达</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link active" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#jiaoyu'"><span class="dropdown-toggle">国际教育</span></a>
            <div class="dropdown-menu m-0">
              <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item">留学申请</a>
              <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item">线上课程</a>
              <a href="InternationalEducation-TopSchools.html" class="dropdown-item active">名校直通车计划</a>
              <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item">语言课程</a>
              <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item">游学、特色团</a>
              <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item">国际学校</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#haiwai'"><span class="dropdown-toggle">海外置业</span></a>
            <div class="dropdown-menu m-0">
              <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
              <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
              <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
              <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#pingtai'"><span class="dropdown-toggle">平台合作</span></a>
            <div class="dropdown-menu m-0">
              <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item">合和法律平台</a>
              <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item">精英留学平台</a>
              <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item">新辉煌金融平台</a>
            </div>
          </div>
          <a href="contact.html" class="nav-item nav-link">联系我们</a>
        </div>
      </div>
    </nav>
  </div>
  <!-- 导航栏区块结束 -->

  <!-- 搜索弹窗区块 -->
  <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
      <div class="modal-content rounded-0">
        <div class="modal-header">
          <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">
            搜索
          </h4>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body d-flex align-items-center">
          <div class="input-group w-75 mx-auto d-flex">
            <input type="search" class="form-control p-3" placeholder="输入关键词搜索" aria-describedby="search-icon-1" />
            <span id="search-icon-1" class="input-group-text p-3"><i class="fa fa-search"></i></span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 搜索弹窗区块结束 -->

  <!-- 主体内容区块：名校计划介绍等 -->
  <div class="container-fluid bg-breadcrumb" style="
        background: linear-gradient(
            rgba(0, 58, 102, 0.9),
            rgba(0, 58, 102, 0.8)
          ),
          url(./img/breadcrumb.png);
        background-position: center center;
        background-repeat: no-repeat;
        background-attachment: initial;
        background-size: cover;
        padding: 100px 0 60px 0;
      ">
    <div class="container text-center py-5" style="max-width: 2000px">
      <h3 class="text-white display-3 mb-4 wow fadeInDown" data-wow-delay="0.1s">
        世界名校定向升学计划<br />
      </h3>
      <h4 style="color: #ffffff">
        （Targeted Admission Plan to Top Global Universities）
      </h4>
      <ol class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown" data-wow-delay="0.3s">
        <li class="breadcrumb-item">
          <a href="index.html" class="text-white">主页</a>
        </li>

        <li class="breadcrumb-item active text-secondary">
          世界名校定向升学计划
        </li>
      </ol>
    </div>
  </div>

  <!-- 主体内容区块：名校计划介绍等 -->
  <div class="container-fluid contact overflow-hidden py-5" style="padding-bottom: 1rem !important">
    <div class="container py-5" style="padding-bottom: 1rem !important">
      <div class="row g-5 mb-5" style="justify-content: flex-end; margin-bottom: 0rem !important">
        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 45%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">美国</h5>
            <h5>&nbsp;</h5>
          </div>
          <h2>ACCP北美名校直通车项目：</h2>
          <h5>&nbsp;</h5>
          <p class="mb-5">
            学生在读高中期间，可选择修读大学认可的学分课程，
            在完成课程的学习并通过考核后，可获得：①美高成绩和毕业文凭；②免标化考试成绩直升北美TOP100名校；③ACCP学分受大学认可。
          </p>
          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>

          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">美国</h5>
            <h5>&nbsp;</h5>
          </div>
          <h2>加州大学欧文分校国际大一课程<br /></h2>
          <h4>（简称U-STAR课程）</h4>
          <h5>&nbsp;</h5>
          <p class="mb-5">
            加州大学（UC）是全美最好的州立大学系统之一，也是世界上最具影响力的公立大学系统，
            是许多留美学生的首选。U-STAR课程学生在读美高期间，可提前修读大学学分课程，享受UC校园资源，
            与其他国际生和本土学生一起参加社团活动，完成课程的学习并通过考核后，可获得：①美高成绩和毕业文凭；
            ②免标化考试成绩直升UC或其他美国名校；③学分受大学认可。
          </p>
          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>

          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">美国</h5>
            <h5>&nbsp;</h5>
          </div>
          <h2>
            约翰·霍普金斯大学（JHU）<br />
            国际教学和全球影响力项目：
          </h2>
          <h5>&nbsp;</h5>
          <p class="mb-5">
            约翰·霍普金斯大学（JHU）是世界顶级私立大学，也是美国第一所研究型大学，全美排名Top10。
            国际教学和全球影响力项目是约翰霍普金斯大学教育学院首个尝试培养国际教育人擦的项目，1年制研究生课程。
            申请学生无本科学术背景要求，无需提供GMAT/GRE，只要GPA达3.0，雅思7分即可申请入学。
            是可实现”双非”院校学生可直升美国“私立常春藤”的稀缺项目。
          </p>
          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>

          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">美国</h5>
            <h5>&nbsp;</h5>
          </div>
          <h2>美国纽约州立大学国际大一课程<br /></h2>
          <h5>（State University of NewYork）</h5>
          <h5>&nbsp;</h5>
          <p class="mb-5">
            该课程可转入纽约州立大学其他分校，包括：石溪分校、宾汉分校、法罗分校，
            成绩优异的学生经辅导老师协助，可申请转入纽约州立大学系统的康奈尔大学（Cornell
            University） 及纽约大学（New York State
            University）、哥伦比亚大学（Colubia University）。
          </p>
          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align:center;">
          <picture>
            <source srcset="图片/国际教育-名校计划2.webp" type="image/webp">
            <img src="图片/国际教育-名校计划2.png" class="img-fluid" alt=""
              style="max-width:100%;height:auto;margin:0 auto;display:block;" />
          </picture>
        </div>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 50%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">英国</h5>
            <h5>&nbsp;</h5>
          </div>
          <p class="mb-5">
            剑桥教育集团成立于1952年，为学生提供高质量的学术课程助力他们成功升读世界一流大学，
            并提供职业机会。新辉煌 ONCAMPUS课程是剑桥教育集团旗下的国际预科品牌。
            ONCAMPUS北英国际预科中心提供三种预科课程。
          </p>

          <h2>本科预科课程/国际大一课程：</h2>

          <p class="mb-5">
            面向高三在读或者已毕业学生开放申请，完成本课程后可享英国北部顶尖大学升读机会及中央兰开夏大学（UCLan）保底名额。
          </p>

          <h2>医学本科预科课程：</h2>

          <p class="mb-5">
            完成本课程后可申请就读中央兰开夏大学医学专业，以及爱尔兰皇家外科医学院、格林纳达圣乔治大学和塞浦路斯尼科西亚大学的所列学位，
            学生也可以通过 UCAS 系统申请其它英国大学。
          </p>

          <h2>研究生预科课程：</h2>

          <p class="mb-5">
            完成本课程的学生可享英国北部顶尖大学升读机会及中央兰开夏大学（UCLan）保底名额。
          </p>
          <div class="col-12">
            <a class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp" data-wow-delay="0.1s"
              href="contact.html#contact">了解更多</a>
          </div>

          <h5>&nbsp;</h5>
          <h5>&nbsp;</h5>

          <h7>（*注：英国北部名校包括，圣安德鲁斯大学、杜伦大学、兰卡斯特大学、利兹大学、约克大学、
            格拉斯哥大学、谢菲尔德大学、邓迪大学、爱丁堡大学、曼彻斯特大学、纽卡斯尔大学等。）</h7>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 ">
          <picture>
            <img src="图片/国际教育-名校计划3.gif" class="img-fluid" alt="" style="max-width: 100%; margin: 10% 0 0 0" />
          </picture>
        </div>
      </div>
    </div>
  </div>

  <!-- Contact Start -->
  <div class="container-fluid contact overflow-hidden py-5" style="padding-bottom: 1rem !important">
    <div class="container py-5" style="padding-bottom: 1rem !important">
      <div class="row g-5 mb-5" style="justify-content: flex-end; margin-bottom: 0rem !important">
        <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center"></div>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 50%; margin-top: 9%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">新辉煌出国</h5>
          </div>
          <h1 class="display-5 mb-4">新辉煌国际教育升学手册</h1>

          <h5>&nbsp; &nbsp;</h5>

          <p class="mb-5">
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出国留学、培训深造、“学人之长补己之短”，是众多莘莘学子们梦寐以求的心愿。中国教育部也保持一贯支持的方针政策，明确“支持留学、鼓励回国、来去自由、发挥作用”。
          </p>
          <p class="mb-5">
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;新辉煌国际教育积极顺应社会及市场发展需求，热诚关注每一位学子的渴求，竭诚为学子们提供完善的留学服务。当下留学环境已发生巨大的变化，我们依托行业前辈十数年的专业经验和资源，提供与时俱进、全面优质的留学项目及服务。
          </p>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align:center;">
          <picture>
            <source srcset="图片/新辉煌留学世界.webp" type="image/webp">
            <img src="图片/新辉煌留学世界.png" class="img-fluid" alt=""
              style="max-width:100%;height:auto;margin:0 auto;display:block;" />
          </picture>
        </div>
      </div>
    </div>
  </div>

  <!-- ========== 底部栏区块 ========== -->
  <div id="footer-placeholder"></div>
  <script>
    fetch('footer.html')
      .then(res => res.text())
      .then(html => { document.getElementById('footer-placeholder').innerHTML = html; });
  </script>
  <!-- 底部栏区块结束 -->

  <!-- ========== 侧边悬浮导航区块 ========== -->
  <div id="custom-service-placeholder"></div>
  <script>
    fetch('components/custom-service.html')
      .then(res => res.text())
      .then(html => {
        document.getElementById('custom-service-placeholder').innerHTML = html;
      });
  </script>
  <!-- 右侧悬浮客服区块结束 -->

  <!-- ========== 本地JS依赖区块 ========== -->
  <script src="js/jquery-3.6.4.min.js"></script>
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="lib/wow/wow.min.js"></script>
  <script src="lib/easing/easing.min.js"></script>
  <script src="lib/waypoints/waypoints.min.js"></script>
  <script src="lib/counterup/counterup.min.js"></script>
  <script src="lib/owlcarousel/owl.carousel.min.js"></script>
  <script src="js/main.js"></script>
  <!-- ========== 本地JS依赖区块 End ========== -->
</body>

</html>
