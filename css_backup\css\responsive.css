/* =========================
  官网移动端响应式样式 responsive.css
  维护说明：
  1. 仅在小屏下做必要适配，桌面端和大部分平板端保持原样式
  2. 按页面/区块功能分组，便于维护
  3. 优先保证内容不溢出、图片自适应、无横向滚动
  ========================= */

/* ========== 1. 导航栏适配 ========== */
@media (max-width: 991.0005px) {
  .navbar-nav {
    flex-direction: column !important;
    align-items: flex-start !important;
  }
}
@media (max-width: 767px) {
  .navbar {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
  }
  .navbar .navbar-toggler {
    order: 1;
    margin-right: 8.0008px;
    margin-left: 0;
    z-index: 10;
  }
  .navbar .navbar-brand {
    order: 2;
    margin-left: 8.0008px;
    flex: 1 1 auto;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  .navbar .navbar-brand img {
    max-height: 40.0004px !important;
  }
  .navbar .navbar-collapse {
    margin-top: 8.0008px;
  }
}

/* ========== 2. 全局基础适配 ========== */
@media (max-width: 767px) {
  html, body {
    max-width: 100vw;
    overflow-x: hidden;
  }
  body {
    font-size: 15.0015px !important;
  }
  .container, .container-fluid {
    padding-left: 6.0006px !important;
    padding-right: 6.0006px !important;
  }
  .row {
    flex-direction: column !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  [class*="col-"] {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  img, video {
    max-width: 100% !important;
    height: auto !important;
    display: block;
  }
}
@media (max-width: 480.0006px) {
  body {
    font-size: 14.0014px !important;
  }
}

/* ========== 3. 轮播图区块 ========== */
@media (max-width: 767px) {
  .carousel-inner .carousel-item img,
  .carousel-item img {
    width: 100vw !important;
    height: 48vw !important;
    max-height: 220.0001px !important;
    object-fit: cover !important;
    object-position: center center !important;
    border-radius: 0 !important;
    margin: 0 auto;
    display: block;
  }
  .carousel-header,
  .carousel,
  .carousel-inner,
  .carousel-item {
    height: auto !important;
    max-height: 188.0005px !important;
  }
  .carousel-caption {
    font-size: 1rem !important;
    background: rgba(0,0,0,0.32) !important;
    padding: .5rem .7rem !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    top: 13% !important;
    width: 100% !important;
    border-radius: 0 0 8.0008px 8.0008px;
    text-align: center !important;
    transform: translateY(-50%) !important;
  }
  .carousel-caption h1,
  .carousel-caption .display-1 {
    font-size: .9rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.5em !important;
    word-break: break-all;
  }
  .carousel-caption h4,
  .carousel-caption .fw-bold {
    font-size: 1rem !important;
    margin-bottom: 0.4em !important;
    word-break: break-all;
  }
  .carousel-caption p,
  .carousel-caption .fs-5 {
    font-size: .8rem !important;
    margin-bottom: 0.5em !important;
    word-break: break-all;
  }
  .carousel-caption .btn {
    font-size: .95rem !important;
    padding: 0.4em 1em !important;
  }
  .carousel-control-prev, .carousel-control-next {
    width: 31.9996px !important;
    height: 31.9996px !important;
    top: 50% !important;
    transform: translateY(-50%);
  }
  .carousel-control-prev-icon, .carousel-control-next-icon {
    width: 23.9987px !important;
    height: 23.9987px !important;
    background-size: 23.9987px 23.9987px !important;
    border-radius: 50%;
    background-color: rgba(60,90,120,0.7) !important;
  }
  .carousel-indicators [data-bs-target],
  .carousel-indicators li {
    width: 5.0005px !important;
    height: 5.0005px !important;
    margin: 0 3.0003px !important;
    border-radius: 50%;
    background-color: #6c7a89 !important;
    opacity: 0.7;
  }
  .carousel-indicators .active {
    background-color: #d32026 !important;
    opacity: 1;
  }
  .carousel-indicators {
    bottom: -10.001px !important;
    left: 35% !important;
    transform: translateX(-50%);
    z-index: 2;
  }
}

/* ========== 4. 标题、按钮、卡片、间距优化 ========== */
@media (max-width: 767px) {
  .display-1, .display-5, h1, h2, h3 {
    font-size: 1.1em !important;
    line-height: 1.3 !important;
    margin-bottom: 0.7em !important;
  }
  .btn {
    font-size: 1em !important;
    padding: 0.5em 1.1em !important;
    border-radius: 999.0014px !important;
  }
  .country-item, .service-item, .training-item, .footer-item {
    border-radius: 10.001px !important;
    box-shadow: 0 2.0002px 8.0008px rgba(0,0,0,0.06);
    margin-bottom: 1.2rem !important;
  }
  .country-item, .service-item, .training-item {
    margin-bottom: 1.5rem !important;
  }
}
@media (max-width: 480.0006px) {
  .display-5, .display-1, h1, h2, h3 {
    font-size: 1.2em !important;
  }
  .btn {
    font-size: .95rem !important;
    padding: 0.5em 1em !important;
  }
}

/* ========== 5. 底部栏 footer 适配 ========== */
@media (max-width: 767px) {
  .footer {
    padding: 1.2rem 0 !important;
    font-size: 0.95em !important;
  }
  .footer .container {
    padding-left: 8.0008px !important;
    padding-right: 8.0008px !important;
  }
  .footer .footer-item {
    margin-bottom: 1.2rem !important;
    padding: .5rem .2rem !important;
    border-radius: 8.0008px !important;
    box-shadow: none !important;
    text-align: center !important;
    align-items: center !important;
    justify-content: center !important;
  }
  .footer .footer-item ul {
    padding-left: 0 !important;
    text-align: center !important;
  }
  .footer .footer-item ul li {
    display: inline-block !important;
    margin: 0 0.3em 0.5em 0.3em !important;
  }
  .footer .footer-item ul li a {
    font-size: 0.98em !important;
    padding: 0.2em 0.5em !important;
  }
  .footer .footer-item p {
    font-size: 0.85em !important;
    text-align: center !important;
    margin-bottom: 0.5em !important;
  }
  .footer .footer-item h4 {
    font-size: 2em !important;
    font-weight: 600 !important;
    margin-bottom: 0.7em !important;
    margin-top: 0.7em !important;
    text-align: center !important;
  }
  .footer .footer-item .mb-3 {
    margin-bottom: 0.5em !important;
  }
  .footer .footer-item .d-flex {
    flex-direction: row !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.7em !important;
    margin-bottom: 0.5em !important;
  }
  .footer .footer-item .d-flex > i {
    font-size: 1.7em !important;
    margin-right: 0.5em !important;
    margin-bottom: 0 !important;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  .footer .footer-item .d-flex > div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    text-align: left !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  .footer .footer-item img {
    margin-bottom: .5rem !important;
    max-width: 80.0007px !important;
    height: auto !important;
  }
  .footer .footer-item .d-flex img {
    display: block;
    margin: 0 auto 0.3em auto !important;
    max-width: 69.9997px !important;
  }
  .footer .footer-item .d-flex .me-4 {
    margin-right: 1em !important;
  }
  .footer .footer-item .text-center {
    text-align: center !important;
    font-size: 0.95em !important;
    margin-top: 0.2em !important;
  }
  .footer .footer-item h6,
  .footer .footer-item p {
    font-size: 0.97em !important;
    margin-bottom: 0.2em !important;
  }
  .bottom-line{
    font-size: 1.1rem;
  }

}

/* ========== 6. about页面/移民规划页面顶部面包屑和主内容区块 ========== */
@media (max-width: 767px) {
  .size_fonts{
    font-size: 11.0011px !important;
  }

  .bg-breadcrumb {
    padding: 1.2rem 0 !important;
    min-height: unset !important;
  }
  .bg-breadcrumb .container {
    padding: 0 8.0008px !important;
  }
  .bg-breadcrumb h3.display-3 {
    font-size: 1.3em !important;
    margin-bottom: 0.7em !important;
    line-height: 1.3 !important;
  }
  .bg-breadcrumb .breadcrumb {
    font-size: 0.98em !important;
    padding: 0.3em 0.5em !important;
    background: rgba(0,0,0,0.08) !important;
    border-radius: 6.0006px !important;
    margin-bottom: 0 !important;
  }
  .bg-breadcrumb .breadcrumb-item {
    padding: 0 0.2em !important;
  }
  .about .row, .container-fluid .row {
    flex-direction: column !important;
    gap: 1.2em !important;
  }
  .about .col-xl-5, .about .col-xl-7, .container-fluid .col-xl-5, .container-fluid .col-xl-7 {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0 !important;
  }
  .about img, .container-fluid img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 8.0008px !important;
  }
  .counter-facts .row, .counter-facts [class*="col-"] {
    flex-direction: row !important;
    flex-wrap: wrap !important;
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  .counter-facts .counter {
    min-height:0px !important;
    margin-bottom: 1.2em !important;
    text-align: center !important;
  }
}

/* ========== 7. 移民规划-国家卡片区块/五大优势counter区块 ========== */
@media (max-width: 991.0005px), (min-width: 992.0007px) {
  .country-flag {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    margin: 0.5em 0 0.2em 0 !important;
    padding: 0 !important;
  }
  .country-flag img {
    display: block !important;
    margin: 0 auto !important;
    max-width: 38.0001px !important;
    height: auto !important;
    box-shadow: 0 1.0001px 4.0004px rgba(0,0,0,0.08);
  }
}
@media (max-width: 767px) {
  .country .row.g-4 {
    flex-direction: column !important;
    gap: 1.5em !important;
  }
  .country-item {
    width: 100% !important;
    margin: 0 auto 1.5em auto !important;
    min-height: 0 !important;
    box-shadow: 0 2.0002px 10.001px rgba(0,0,0,0.07);
    border-radius: 12.0012px !important;
    padding: 0.7em 0.5em !important;
  }
  .country-item img {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 8.0008px !important;
  }
  .country-item h2 {
    font-size: 1.25em !important;
    margin-bottom: 0.5em !important;
  }
  .country-name {
    font-size: 1.05em !important;
    padding: 0.5em 0.2em !important;
    width: 100% !important;
    text-align: left !important;
  }
  .country .btn {
    font-size: 1.1em !important;
    padding: 0.6em 1.2em !important;
    margin-top: 0.7em !important;
  }
  .country-item h2, .country-item b {
    font-size: 1.13em !important;
  }
  .country-item a.hover {
    font-size: 0.98em !important;
  }
  .country-item > div[style*="position: absolute"] h2 {
    font-size: 1.1em !important;
    padding: 0.2em 0.5em !important;
  }
  .counter-facts .row.g-4 {
    flex-wrap: wrap !important;
    flex-direction: row !important;
    justify-content: center !important;
    gap: 0.7em !important;
  }
  .counter-facts [class*="col-"] {
    width: 48% !important;
    max-width: 48% !important;
    margin-bottom: 1.1em !important;
    padding: 0 !important;
  }
  .counter-facts .counter {
    text-align: center !important;
    padding: 0.7em 0.2em !important;
    border-radius: 10.001px !important;
    box-shadow: 0 2.0002px 8.0008px rgba(0,0,0,0.06);
  }
  .counter-facts .counter-icon img {
    max-width: 54.0018px !important;
    margin: 0 auto 0.5em auto !important;
  }
  .counter-facts h3 {
    font-size: 1.08em !important;
    margin-bottom: 0.4em !important;
  }
  .img_index{
    width:33% !important;
  }
  .img_index>.carousel-inner{
    border-radius: 0 !important;
  }
}
/* ========== 8. 海外置业/正文区块/图片/溢出兜底 ========== */
@media (max-width: 767px) {
  body, html, .container, .container-fluid {
    max-width: 100vw !important;
    overflow-x: hidden !important;
  }
  .main-content, .content, .article-content, .project-content, .globe_subject_content, .news-content, .newsContent, .project_page_cq_new, .cq_content, .cq_main, .cq_article, .cq_detail, .cq_detail_content {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    word-break: break-all !important;
    white-space: normal !important;
    padding-left: 8.0008px !important;
    padding-right: 8.0008px !important;
    box-sizing: border-box !important;
  }
  .main-content p, .content p, .article-content p, .project-content p, .globe_subject_content p, .news-content p, .newsContent p, .project_page_cq_new p, .cq_content p, .cq_main p, .cq_article p, .cq_detail p, .cq_detail_content p,
  .main-content h1, .main-content h2, .main-content h3, .main-content h4, .main-content h5, .main-content h6,
  .content h1, .content h2, .content h3, .content h4, .content h5, .content h6,
  .article-content h1, .article-content h2, .article-content h3, .article-content h4, .article-content h5, .article-content h6 {
    word-break: break-all !important;
    white-space: normal !important;
    max-width: 100% !important;
    overflow-x: auto !important;
  }
  .main-content img, .content img, .article-content img, .project-content img, .globe_subject_content img, .news-content img, .newsContent img, .project_page_cq_new img, .cq_content img, .cq_main img, .cq_article img, .cq_detail img, .cq_detail_content img {
    max-width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 0 auto 1em auto !important;
  }
  /* 强制所有页面所有元素不超出屏幕宽度，彻底防止横向滚动和文字溢出 */
  html, body, .container, .container-fluid, .row, [class*="col-"], section, article, div, p, span, h1, h2, h3, h4, h5, h6 {
    max-width: 100vw !important;
    box-sizing: border-box !important;
    word-break: break-all !important;
    white-space: normal !important;
  }
  /* 针对 style="width:950.0001px" 等内联宽度，强制覆盖 */
  [style*="width:950.0001px"], [style*="width: 950.0001px"] {
    width: 100% !important;
    max-width: 100vw !important;
  }
  .Breadcrumbs{
    height:auto;
  }
  /* 针对 .b-content、.introduction_1 等常见正文区块再兜底 */
  .b-content, .introduction_1, .main-content, .content, .article-content, .project-content, .globe_subject_content, .news-content, .newsContent, .project_page_cq_new, .cq_content, .cq_main, .cq_article, .cq_detail, .cq_detail_content {
    max-width: 100vw !important;
    overflow-x: hidden !important;
    word-break: break-all !important;
    white-space: normal !important;
    padding-left: 8.0008px !important;
    padding-right: 8.0008px !important;
    box-sizing: border-box !important;
  }
  /* 彻底禁止正文区块和段落出现横向滚动条和独立滑动，保证文字自然换行、页面整体只竖向滚动 */
  .b-content, .introduction_1, .main-content, .content, .article-content, .project-content, .globe_subject_content, .news-content, .newsContent, .project_page_cq_new, .cq_content, .cq_main, .cq_article, .cq_detail, .cq_detail_content,
  section, article, div, p, span, h1, h2, h3, h4, h5, h6 {
    max-width: 100vw !important;
    box-sizing: border-box !important;
    word-break: break-all !important;
    white-space: normal !important;
    -webkit-overflow-scrolling: auto !important;
    scrollbar-width: none !important;
  }
  /* 移除所有段落和区块的scroll、auto等溢出属性 */
  [style*="overflow-x: auto"], [style*="overflow-x:auto"], [style*="overflow: auto"], [style*="overflow:auto"], [style*="overflow-x:scroll"], [style*="overflow-x: scroll"] {
    overflow-x: visible !important;
    overflow-y: visible !important;
  }
  /* 常用图片兜底 */
  .cancelimg {
    width: 100% !important;
    max-width: 100vw !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin: 0 auto 1em auto !important;
    top: 0 !important;
    position: static !important;
  }
  .cancelimg img {
    max-width: 96vw !important;
    width: 100% !important;
    height: auto !important;
    display: block !important;
    margin: 0 auto !important;
  }
  .b-content .b-left{
    width: 23rem !important;
  }
  .introduction_1 {
    width: 100% !important;
    max-width: 100vw !important;
    box-sizing: border-box !important;
    padding-left: 8px !important;
    padding-right: 8px !important;
    overflow-x: hidden !important;
  }
  .introduction_1 span {
    font-size: 1.1em !important;
    word-break: break-all !important;
  }
  .introduction_1 p {
    font-size: 0.98em !important;
    word-break: break-all !important;
    white-space: normal !important;
    max-width: 100% !important;
    overflow-x: auto !important;
  }
}
@media (max-width: 767px) {
  .py-5 {
    padding-bottom: 0 !important;
  }
  .text-secondary2{
    font-size: 2rem !important;
  }
  .b-content .b-left {
    width: 100% !important;
    max-width: 100vw !important;
    min-width: 0 !important;
    float: none !important;
    position: static !important;
    margin: 0 0 1.2em 0 !important;
    padding: 0 !important;
    z-index: auto !important;
    background: none !important;
    display: block !important;
  }
  .b-content .b-left p,
  .b-content .b-left .two {
    width: 100% !important;
    max-width: 100vw !important;
    word-break: break-all !important;
    white-space: normal !important;
    font-size: 1em !important;
    margin: 0 0 0.8em 0 !important;
    padding: 0 !important;
    background: none !important;
    display: block !important;
  }
}

/* ========== 9. 侧边客服/二维码等悬浮区块 ========== */
@media (max-width: 767px) {
  .CustomService {
    right: .5rem !important;
    bottom: .5rem !important;
    width: 44.0008px !important;
    height: auto !important;
  }
  .CustomService .CusSer_con ul {
    padding-left: 0 !important;
  }
  .CustomService .li3 .li_code {
    left: -120.0011px !important;
    width: 120.0011px !important;
    font-size: 0.9em !important;
  }
}

/* ========== 美国移民流程区块移动端美化 ========== */
@media (max-width: 767px) {
  .application_eb5{
    margin:90px 0 !important;
  }
  .hidden_title{
    display: none !important;
  }
  .b-content .b-right{
    position:relative;
  }
  .size-breadcrumb{
    padding: 5rem !important;
  }
  .Application-process .process-content-new {
    padding: 0 0.5em !important;
  }
  .process_list-new15 {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    width: 100% !important;
    margin: 0 auto !important;
    gap: 0.5em !important;
  }
  .process_list-new15 .process_cicle {
    width: 98%;
    max-width: 370.0005px;
    min-width: 0;
    background: #fff;
    border-radius: 16.0016px;
    box-shadow: 0 2.0002px 10.001px rgba(0,0,0,0.07);
    margin: 1.2em 0 !important;
    padding: 1em 0.7em 0.7em 0.7em !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    position: relative;
    float: none !important;
    clear: both !important;
    left: 0 !important;
    right: 0 !important;
  }
  .process_list-new15 .step_and_title {
    display: flex;
    flex-direction: column;
    align-items: center !important;
    width: 100%;
    margin-left: 0;
    text-align: center !important;
  }
  .process_list-new15 .step {
    width: 2.2em;
    height: 2.2em;
    background: #003a66;
    color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1em;
    font-weight: bold;
    margin: 0 auto 0.5em auto;
    flex-shrink: 0;
    position: relative;
    left: 0;
    top: 0;
    float: none;
  }
  .process_list-new15 .title {
    font-size: 1.08em !important;
    font-weight: 600;
    margin-bottom: 0.3em;
    color: #003a66;
    word-break: break-all;
    text-align: center !important;
  }
  .process_list-new15 .desc {
    font-size: 0.98em !important;
    color: #333;
    line-height: 1.7;
    margin-bottom: 0.2em;
    word-break: break-all;
    text-align: center !important;
  }
  /* 移除所有流程区块 float/clear/width/margin-bottom 内联样式 */
  .process_list-new15 [style*="float"],
  .process_list-new15 [style*="clear"],
  .process_list-new15 [style*="width"],
  .process_list-new15 [style*="margin-bottom"] {
    float: none !important;
    clear: none !important;
    width: 100% !important;
    margin-bottom: 0 !important;
    position: static !important;
    left: 0 !important;
    right: 0 !important;
    display: block !important;
  }
  /* 统一移动端字体、按钮、间距 */
  body {
    font-size: 15.0015px !important;
  }
  .btn {
    font-size: 1em !important;
    padding: 0.5em 1.1em !important;
    border-radius: 999.0014px !important;
  }
  h1, h2, h3, h4, h5, h6 {
    font-size: 1.1em !important;
    margin-bottom: 0.7em !important;
    line-height: 1.3 !important;
  }
}

/* ========== 香港项目优势按钮移动端排版优化 ========== */
@media (max-width: 767px) {
  .project_advantage_2_main_btn {
    display: flex !important;
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 12.0012px !important;
    margin-top: 18.0018px !important;
    margin-bottom: 8.0008px !important;
  }
  .project_advantage_2_main_btn a {
    width: 100% !important;
    box-sizing: border-box;
    font-size: 1.08em !important;
    padding: 5.0005px 0 !important;
    border-radius: 999.0014px !important;
    text-align: center !important;
    margin: 0 !important;
  }
}

/* ========== 首页counter区块移动端一行显示优化 ========== */
@media (max-width: 767px) {
  .container{
    margin-right:0px !important;
  }
  .width_fonts{
    width:69.9997px !important;
  }
  .counter-facts .counter:before{
    left:3.0003px !important;
  }
  .counter {
    width: 64%;
    left: 17.0017px;
    margin-bottom: 0 !important;
  }
  .row.g-4[style*="flex-wrap: nowrap"] {
    flex-wrap: nowrap !important;
    overflow-x: unset !important;
    gap: 0 !important;
    --bs-gutter-x: 0 !important;
    justify-content: center !important;
    align-items: stretch !important;
    width: 100vw !important;
    margin-left: -19.9984px !important;
    margin-right: -12.0012px !important;
  }
  .row.g-4[style*="flex-wrap: nowrap"] > [class*="col-"] {
    flex: 1 1 0 !important;
    width: 19vw !important;
    min-width: 0 !important;
    max-width: 19vw !important;
    margin: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    box-sizing: border-box !important;
  }
  .counter {
    margin-bottom: 0 !important;
    padding: 0 1.0001px !important;
    min-width: 0 !important;
    box-shadow: none !important;
  }
  .counter-icon img {
    max-width: 38vw !important;
    height: auto !important;
    margin: 0 auto !important;
    display: block !important;
  }
  .counter-content h3 {
    font-size: 0.68em !important;
    margin: 4.0004px 0 1.0001px 0 !important;
    word-break: break-all;
    line-height: 1.05 !important;
  }
}

/* 首页counter单卡片移动端自适应缩放，彻底防止重叠 */
@media (max-width: 767px) {
  .counter {
    padding: 0 2.0002px !important;
    min-width: 0 !important;
    box-shadow: none !important;
    background: none !important;
  }
  .counter-icon img {
    max-width: 32vw !important;
    height: auto !important;
    margin: 0 auto !important;
    display: block !important;
  }
  .counter-content h3 {
    font-size: 0.62em !important;
    margin: 3.0003px 0 1.0001px 0 !important;
    word-break: break-all;
    line-height: 1.05 !important;
    padding: 0 !important;
    text-align: center !important;
  }
  .col-12.col-sm-6.col-md-6.col-xl-3 {
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin: 0 !important;
    min-width: 0 !important;
    max-width: 20vw !important;
    width: 20vw !important;
    flex: 1 1 0 !important;
    box-sizing: border-box !important;
  }
}

/* ========== 强力覆盖 py-5 移动端 padding-bottom，优先级最高 */
@media (max-width: 767px) {
  body .py-5 {
    padding-bottom: 0 !important;
  }
  html body .py-5 {
    padding-bottom: 0 !important;
  }
  .container.py-5, .container-fluid.py-5 {
    padding-bottom: 0 !important;
  }
}

/* 底部栏整体间距紧凑化，移动端和桌面端都生效 */
.footer, .footer-item, .footer .container, .footer .row, .footer .col-md-6, .footer .col-lg-3, .footer .col-xl-3 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.footer-item > h4, .footer-item > h6, .footer-item > div, .footer-item > ul, .footer-item > .row {
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.footer .mb-4, .footer .mb-3, .footer .mb-2, .footer .mt-4, .footer .mt-3 {
  margin-top: 0.5em !important;
  margin-bottom: 0.5em !important;
}
.footer .row > [class*="col-"] {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}
@media (max-width: 767px) {
  .footer, .footer .container, .footer .row, .footer .footer-item {
    padding-top: 2.0002px !important;
    padding-bottom: 2.0002px !important;
  }
  .footer-item > h4, .footer-item > h6, .footer-item > div, .footer-item > ul, .footer-item > .row {
    margin-top: 0.08em !important;
    margin-bottom: 0.08em !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .footer .mb-4, .footer .mb-3, .footer .mb-2, .footer .mt-4, .footer .mt-3 {
    margin-top: 0.08em !important;
    margin-bottom: 0.08em !important;
  }
  .footer .row > [class*="col-"] {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }
  .footer .d-flex {
    margin-bottom: 0 !important;
    margin-top: 0 !important;
    gap: 0.1em !important;
  }
  .footer .text-center, .footer .text-white, .footer .text-muted {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
}

/* =====================
   底部栏快捷索引分组间距极致收紧（移动端）
   ===================== */
@media (max-width: 768.0001px) {
  /* 针对底部栏快捷索引分组，进一步收紧上下间距 */
  .footer-quick-index .footer-section,
  .footer-quick-index .footer-group {
    margin-top: 10.001px !important;
    margin-bottom: 10.001px !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .footer-quick-index hr {
    margin-top: 8.0008px !important;
    margin-bottom: 8.0008px !important;
  }
}

/* 移动端footer快捷索引区块分组间距极致收紧 */
@media (max-width: 767px) {
  .footer .footer-item .row > .col-6 {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }
  .footer .footer-item .row {
    margin-bottom: -69.9997px !important;
    padding-bottom: 0 !important;
    gap: 0 !important;
  }
  .footer .footer-item h6 {
    margin-bottom: 0.2em !important;
    padding-bottom: 2.0002px !important;
  }
  .footer .footer-item ul {
    margin-bottom: 0 !important;
    padding-bottom: 0 !important;
  }
  .footer .footer-item ul + h6,
  .footer .footer-item ul + ul {
    margin-top: 0.2em !important;
    padding-top: 0 !important;
  }
  .footer .footer-item .row + .row {
    margin-top: 0.2em !important;
    padding-top: 0 !important;
  }
  .footer .footer-item > .row + h4,
  .footer .footer-item > .row + h6 {
    margin-top: 0.3em !important;
    padding-top: 0 !important;
  }
  .footer .footer-item h4 {
    margin-bottom: 0.3em !important;
    margin-top: 0.3em !important;
  }
  /* 额外兜底，去除分组间大空隙 */
  .footer .footer-item > * {
    margin-top: 0.2em !important;
    margin-bottom: 0.2em !important;
  }
}

/* ========== END ========== */

@media (max-width: 767px) {
  /* footer快捷索引区块四个标题颜色统一 */
  .footer .footer-item .row > .col-6 h6 {
    color: #d32026  !important;
  }
  .footer .footer-item .row.footer_bottom > .col-6 h6 {
    color: #d32026  !important;
  }
}

@media (max-width: 767px) {
  .counter-features {
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
  .counter-features-row {
    flex-wrap: wrap !important;
    justify-content: center !important;
    --bs-gutter-x: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  .counter-features-row > [class^="col-"] {
    width: 44vw !important;
    max-width: 44vw !important;
    margin-bottom: 4vw !important;
    margin-left: 2vw !important;
    margin-right: 2vw !important;
    display: inline-block !important;
    float: none !important;
  }
  .counter-features .counter {
    width: 40vw !important;
    height: 40vw !important;
    min-width: 40vw !important;
    min-height: 40vw !important;
    max-width: 40vw !important;
    max-height: 40vw !important;
    border-radius: 50% !important;
    box-shadow: 0 2.0002px 12.0012px rgba(0,0,0,0.10);
    padding: 0 !important;
    background: #fff;
    margin: 0 auto;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
  }
  .counter-features .counter-icon img {
    max-width: 38% !important;
    margin: 0 auto 0.5em auto !important;
    display: block;
  }
  .counter-features .counter-content h3 {
    font-size: 0.95em !important;
    margin: 0.3em 0 0 0 !important;
    letter-spacing: .5px;
    color: #d32026 !important;
    font-weight: 600;
    line-height: 1.1;
    text-align: center;
    word-break: break-all;
  }
}

/* counter-features 双结构显示控制 */
.counter-features-mobile { display: none; }
@media (max-width: 767px) {
  .counter-features-pc { display: none !important; }
  .counter-features-mobile { display: block !important; }
  .counter-features-mobile-list {
    display: flex;
    flex-wrap: nowrap;
    justify-content: space-between;
    gap: 0;
    padding: 0 2vw;
    overflow-x: hidden;
    width: 100%;
    box-sizing: border-box;
  }
  .counter-mobile-item {
    flex: 1 1 0;
    max-width: 20%;
    display: flex;
    justify-content: center;
    margin-bottom: 0;
  }
  .counter-mobile-circle {
    width: 18vw;
    height: 18vw;
    border-radius: 50%;
    background: linear-gradient(135deg, #4fc3f7 0%, #e3f2fd 60%, #ffffff 100%);
    box-shadow: 0 2.0002px 12.0012px rgba(0,0,0,0.10);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    padding: 0;
  }
  .counter-mobile-circle img {
    width: 60%;
    max-width: 60%;
    margin: 0 auto 0.15em auto;
    display: block;
  }
  .counter-mobile-title {
    font-size: 0.65em;
    color: #1766a6;
    font-weight: 600;
    margin-top: 0.05em;
    line-height: 1.1;
    word-break: break-all;
    max-width: 80%;
    white-space: normal;
    text-align: center;
  }
}

/* 核心优势区块轮播与counter-features模块间距优化 */
.counter-facts > div[style*="flex-wrap: nowrap"] {
  margin-bottom: 2.5rem;
}
@media (max-width: 767px) {
  .counter-facts > div[style*="flex-wrap: nowrap"] {
    margin-bottom: 1.5rem;
  }
  #counter-features-container {
    margin-top: 1.2rem;
  }
}

/* counter-facts区块内counter-features组件上下间隔优化 */
.counter-facts #counter-features-container {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
@media (max-width: 767px) {
  .counter-facts #counter-features-container {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 767px) {
  .btn.btn-primary {
    display: block;
    width: 90vw;
    max-width: 319.9991px;
    margin: .6rem auto 1.2rem auto;
    font-size: 1.08em;
    padding: 0.9em 0 !important;
    border-radius: 999.0014px !important;
    text-align: center;
    box-shadow: 0 2.0002px 8.0008px rgba(0,0,0,0.07);
    letter-spacing: 1.0001px;
  }
}

@media (max-width: 767px) {
  .b-content {
    display: flex !important;
    flex-direction: column !important;
    flex-wrap: nowrap !important;
    align-items: stretch !important;
  }
  .b-content .b-left,
  .b-content .b-right {
    float: none !important;
    width: 100% !important;
    max-width: 100% !important;
    min-width: 0 !important;
    margin: 0 0 1.2em 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    position: static !important;
    z-index: auto !important;
    background: none !important;
    display: block !important;
  }
  .b-content .b-right {
    margin-bottom: 0 !important;
  }
}

/* Breadcrumbs区块内b-content模块间距优化 */
.Breadcrumbs .b-content {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
@media (max-width: 767px) {
  .Breadcrumbs .b-content {
    margin-top: 1.5rem;
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .project_advantage_2_rl {
    flex-direction: column;
    align-items: stretch; /* 或center，根据需求 */
  }
  .project_advantage_2_rl picture {
    margin-right: 0;
    margin-bottom: 16px; /* 图片和文字上下间距 */
    width: 100%;         /* 可选：让图片宽度自适应 */
    text-align: center;  /* 可选：图片居中 */
  }
}

/* 首页头部大标题区块优化 */
@media (max-width: 767px) {
  /* 首页头部大标题区块优化 */
  .index-hero-title h4 {
    font-size: 0.92em !important;
    margin-bottom: 0.6em !important;
  }
  .index-hero-title h1 {
    font-size: 1.3em !important;
    margin-bottom: 0.6em !important;
  }
  .index-hero-title p {
    font-size: 0.85em !important;
    margin-bottom: 0.8em !important;
  }
  .index-hero-title .btn {
    min-width: 0 !important;
    width: auto !important;
    max-width: 80vw !important;
    padding-left: 1.2em !important;
    padding-right: 1.2em !important;
    padding-top: 0.5em !important;
    padding-bottom: 0.5em !important;
    display: inline-block !important;
  }
  /* 首页头部大标题区块整体下移 */
  .index-hero-title {
    margin-top: 2.2em !important;
  }
}

/* 首页下方"了解更多"按钮样式优化 */
@media (max-width: 767px) {
  .btn.btn-primary.rounded-pill {
    font-size: 0.92em !important;
    min-width: 0 !important;
    width: auto !important;
    max-width: 80vw !important;
    padding-left: 1.2em !important;
    padding-right: 1.2em !important;
    padding-top: 0.5em !important;
    padding-bottom: 0.5em !important;
    display: inline-block !important;
  }
}
