﻿<!-- ========== 新辉煌出国 InternationalEducation-OnlineCourses.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <!-- ========== 头部meta与样式引入区块 ========== -->
    <meta charset="utf-8" />
    <title>新辉煌出国</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="" name="keywords" />
    <meta content="" name="description" />
    <link href="css/responsive.css" rel="stylesheet" />
    <!-- 本地图标字体，已本地化 -->
    <link rel="stylesheet" href="css/fontawesome/all.css" />
    <link rel="stylesheet" href="css/bootstrap-icons/bootstrap-icons.css" />

    <!-- 本地动画库、轮播库样式 -->
    <link href="lib/animate/animate.min.css" rel="stylesheet" />
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />

    <!-- 结构化区块样式 -->
    <link href="css/css4/gg1.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/merged/reset1.css" />
    <link rel="stylesheet" href="./css/merged/common1.css" />
    <link rel="stylesheet" href="./css/merged/window1.css" />
    <link rel="stylesheet" href="./css/merged/advisers1.css" />
    <link rel="stylesheet" href="./css/merged/flag_window.css" />
    <link rel="stylesheet" href="./css/merged/flag.css" />
    <link rel="stylesheet" href="./css/merged/swiper.css" />
    <link rel="stylesheet" href="./css/merged/globe_subject.css" />
    <link rel="stylesheet" href="./css/merged/public.css" />
    <link rel="stylesheet" href="./css/merged/index_new.css" />
    <link rel="stylesheet" href="./css/merged/project_page_cq_new.css" />
    <link rel="stylesheet" href="./css/merged/jinrErweima.css" />
    <link rel="stylesheet" href="./css/merged/flag(1).css" />

    <!-- 本地Bootstrap样式 -->
    <link href="css/bootstrap.min.css" rel="stylesheet" />

    <!-- 站点主样式，结构化注释 -->
    <link href="css/style.css" rel="stylesheet" />

    <link rel="shortcut icon" href="图片/新辉煌logo.png" />

    <style>
      /* 隐藏style2样式 */
      .style2 {
        display: none;
      }
      .hover:hover {
        color: var(--bs-secondary) !important;
      }
    </style>
    <!-- ========== 头部meta与样式引入区块 End ========== -->
  </head>

  <body>
    <!-- ========== 顶部加载动画区块 ========== -->
    <div
      id="spinner"
      class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center"
    >
      <div
        class="spinner-border text-secondary"
        style="width: 3rem; height: 3rem"
        role="status"
      >
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <!-- 顶部加载动画区块结束 -->

    <!-- 顶部栏区块 -->
    <div class="container-fluid bg-primary px-5 d-none d-lg-block">
      <div class="row gx-0 align-items-center">
        <div class="col-lg-5 text-center text-lg-start mb-lg-0">
          <div class="d-flex"></div>
        </div>
        <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
        <div class="col-lg-4 text-center text-lg-end">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
      </div>
    </div>
    <!-- 顶部栏区块结束 -->

    <!-- 导航栏区块 -->
    <div class="container-fluid nav-bar p-0">
      <nav
        class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0"
      >
        <a href="" class="navbar-brand p-0">
          <h1 class="display-5 text-secondary m-0">
            <picture>
              <source srcset="图片/logo.webp" type="image/webp">
              <img src="图片/logo.png" class="img-fluid" alt="" style="max-height: 80px" />
            </picture>
          </h1>
          <!-- <img src="img/logo.png" alt="Logo"> -->
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarCollapse"
        >
          <span class="fa fa-bars"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
          <div class="navbar-nav ms-auto py-0">
            <a href="index.html" class="nav-item nav-link">首页</a>
            <a href="about.html" class="nav-item nav-link">关于我们</a>
            <div class="nav-item dropdown">
              <a href="Immigration.html" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='Immigration.html'">
                <span class="dropdown-toggle">移民项目</span>
              </a>
              <div class="dropdown-menu m-0">
                <a href=" Immigration-USA.html" class="dropdown-item"
                  >美国</a
                >
                <a href="Immigration-Canada.html" class="dropdown-item"
                  >加拿大</a
                >
                <a href="Immigration-Ireland.html" class="dropdown-item"
                  >爱尔兰</a
                >
                <a href="Immigration-HongKong.html" class="dropdown-item"
                  >香港</a
                >
                <a href="Immigration-SingaporeEP.html" class="dropdown-item"
                  >新加坡</a
                >
                <a href="Immigration-Grenada.html" class="dropdown-item"
                  >格林纳达</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a href="#" class="nav-link active" data-bs-toggle="dropdown" onclick="window.location.href='index.html#jiaoyu'"
                ><span class="dropdown-toggle">国际教育</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item"
                  >留学申请</a
                >
                <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item active"
                  >线上课程</a
                >
                <a href="InternationalEducation-TopSchools.html" class="dropdown-item"
                  >名校直通车计划</a
                >
                <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item"
                  >语言课程</a
                >
                <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item"
                  >游学、特色团</a
                >
                <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item"
                  >国际学校</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#haiwai'"
                ><span class="dropdown-toggle">海外置业</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
                <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
                <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
                <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
              </div>
            </div>
            <div class="nav-item dropdown">
              <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#pingtai'"
                ><span class="dropdown-toggle">平台合作</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item"
                  >合和法律平台</a
                >
                <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item"
                  >精英留学平台</a
                >
                <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item"
                  >新辉煌金融平台</a
                >
              </div>
            </div>
            <a href="contact.html" class="nav-item nav-link">联系我们</a>
          </div>
        </div>
      </nav>
    </div>
    <!-- 导航栏区块结束 -->

    <!-- 搜索弹窗区块 -->
    <div
      class="modal fade"
      id="searchModal"
      tabindex="-1"
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content rounded-0">
          <div class="modal-header">
            <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">
              搜索
            </h4>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body d-flex align-items-center">
            <div class="input-group w-75 mx-auto d-flex">
              <input
                type="search"
                class="form-control p-3"
                placeholder="输入关键词搜索"
                aria-describedby="search-icon-1"
              />
              <span id="search-icon-1" class="input-group-text p-3"
                ><i class="fa fa-search"></i
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 搜索弹窗区块结束 -->

    <!-- 主体内容区块：线上课程介绍等 -->
    <div
      class="container-fluid bg-breadcrumb"
      style="
        background: linear-gradient(
            rgba(0, 58, 102, 0.9),
            rgba(0, 58, 102, 0.8)
          ),
          url(./img/breadcrumb.png);
        background-position: center center;
        background-repeat: no-repeat;
        background-attachment: initial;
        background-size: cover;
        padding: 100px 0 60px 0;
      "
    >
      <div class="container text-center py-5" style="max-width: 2000px">
        <h3
          class="text-white display-3 mb-4 wow fadeInDown"
          data-wow-delay="0.1s"
        >
          全球精选线上课程<br />
        </h3>
        <h2 style="color: #ffffff">（Selected Global Online Courses）</h2>
        <ol
          class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown"
          data-wow-delay="0.3s"
        >
          <li class="breadcrumb-item">
            <a href="index.html" class="text-white">主页</a>
          </li>

          <li class="breadcrumb-item active text-secondary">
            全球精选线上课程
          </li>
        </ol>
      </div>
    </div>

    <!-- Contact Start -->
    <div
      class="container-fluid contact overflow-hidden py-5"
      style="padding-bottom: 1rem !important"
    >
      <div class="container py-5" style="padding-bottom: 1rem !important">
        <div
          class="row g-5 mb-5"
          style="justify-content: flex-end; margin-bottom: 0rem !important"
        >
          <div
            class="col-lg-6 wow fadeInLeft"
            data-wow-delay="0.1s"
            style="max-width: 50%"
          >
            <div class="sub-style">
              <h5 class="sub-title text-primary pe-3">美国</h5>
              <h5>&nbsp;</h5>
            </div>
            <h2>斯坦福在线高中<br /></h2>
            <h5>（Stanford Online High School）：</h5>
            <h5>&nbsp;</h5>
            <p class="mb-5">
              是一所拥有着顶尖大学斯坦福的优质教学资源的私立学校，为学生提供严格的大学预科课程，学生来自美国47个州和全球43个国家。
              斯坦福在线高中招收7-12年级有学术天赋的学生，提供25门AP课程和大学级别的课程，超过65%的教师是博士学位。
              学生 SAT平均分是1500分，ACT平均分是33分。
            </p>
            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                href="contact.html#contact"
                >了解更多</a
              >
            </div>

            <h5>
              &nbsp;<br />
              &nbsp;
            </h5>

            <div class="sub-style">
              <h5
                class="sub-title text-primary pe-3"
                style="padding-right: 0rem !important"
              ></h5>
              <h5>&nbsp;</h5>
            </div>
            <h2>得克萨斯大学奥斯汀分校高中<br /></h2>
            <h6>（The University of Texas at Austin High School）：</h6>
            <h5>&nbsp;</h5>
            <p class="mb-5">
              为学生提供线上课程。目前，学校有300多名全日制学生，并与全球250多个学区建立了合作关系。
              得克萨斯大学奥斯汀分校高中招收9-12年级学生，在60个科目领域提供课程，包括在线的AP课程或者IB课程。
              学生 SAT平均分是1170分。
            </p>
            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                hhref="contact.html#contact"
                >了解更多</a
              >
            </div>

            <h5>
              &nbsp;<br />
              &nbsp;
            </h5>

            <div class="sub-style">
              <h5
                class="sub-title text-primary pe-3"
                style="padding-right: 0rem !important"
              ></h5>
              <h5>&nbsp;</h5>
            </div>
            <h2>乔治华盛顿大学在线高中<br /></h2>
            <h6>（George Washington University Online High School）：</h6>
            <h5>&nbsp;</h5>
            <p class="mb-5">
              是一所私立大学预科在线高中，是美国第一所研究型大学附属线上高中。
              乔治华盛顿大学在线高中为8-12年级有学术天赋的学生提供获得高中文凭的机会，
              提供16门AP课程和5种世界语言的教学。学生SAT平均分是1220分，ACT的平均分是26分。
            </p>
            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                href="contact.html#contact"
                >了解更多</a
              >
            </div>

            <h5>
              &nbsp;<br />
              &nbsp;
            </h5>
          </div>

          <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 ">
            <picture>
              <source srcset="图片/国际教育-线上课程1.webp" type="image/webp">
              <img src="图片/国际教育-线上课程1.png" class="img-fluid" alt="" style="max-width: 100%; margin: 0% 0 0 0" />
            </picture>
          </div>

          <div
            class="col-lg-6 wow fadeInLeft"
            data-wow-delay="0.1s"
            style="max-width: 50%"
          >
            <div class="sub-style">
              <h5
                class="sub-title text-primary pe-3"
                style="padding-right: 0rem !important"
              ></h5>
              <h5>&nbsp;</h5>
            </div>
            <h2>加州大学欧文分校<br /></h2>
            <h6>（University of California,IRVINE，简称UCI）<br /></h6>
            <h5>&nbsp;</h5>
            <h3>高级写作网课<br /></h3>
            <h6>（Online-Advanced Writing Cmoposition）：</h6>
            <h5>&nbsp;</h5>
            <p class="mb-5">
              加州大学欧文分校国际大一课程（即UCI的USTAR课程）
              独家开设高级写作课程，该课程为USTAR课程必修课程，学生可在国内线上学习，
              完成课程后可获结业证书和UCI的正式大学成绩单，并豁免2个加州大学学分。
              这是加州大学唯一官方认可并可获得学分的线上课程。
            </p>
            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                href="contact.html#contact"
                >了解更多</a
              >
            </div>
          </div>

          <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 ">
            <picture>
              <img src="图片/国际教育-线上课程2.gif" class="img-fluid" alt="" style="max-width: 100%; margin: 0% 0 0 0" />
            </picture>
          </div>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>

          <div
            class="col-lg-6 wow fadeInLeft"
            data-wow-delay="0.1s"
            style="max-width: 50%"
          >
            <div class="sub-style">
              <h5 class="sub-title text-primary pe-3">英国</h5>
              <h5>&nbsp;</h5>
            </div>
            <h2>精英课程计划<br /></h2>
            <h5>（ Elite Programme ）UKISET：</h5>
            <h5>&nbsp;</h5>
            <p class="mb-5">
              专门为入读英国贵族私立学校的学生提供入学考试培训，是一项领导UKISET
              （中文“译赛”，即非英籍学生“英国私立学校入学考试”）、CE考试（即英国本土学生私立中学“普通学业考试”）
              和英国寄宿学校入学考试准备工作的教育计划。该计划由新辉煌国际教育联合英国格林威治国际教育发起，
              英国顶级教育机构Gabbitas（加比达斯）授课。可提供线上培训和线下沉浸式英语教学，满足不同学生的需求。
              线下于英国著名私立中学St
              LawrenceCollege（圣劳伦斯学院）校内授课及住宿，享受最纯正英式教育环境。
            </p>
            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                hhref="contact.html#contact"
                >了解更多</a
              >
            </div>
          </div>

          <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 ">
            <picture>
              <source srcset="图片/国际教育-线上课程5.webp" type="image/webp">
              <img src="图片/国际教育-线上课程5.jpg" class="img-fluid" alt="" style="max-width: 100%; margin: 0% 0 0 0" />
            </picture>
          </div>

          <h5>
            &nbsp;<br />
            &nbsp;
          </h5>

          <div
            class="col-lg-6 wow fadeInLeft"
            data-wow-delay="0.1s"
            style="max-width: 50%"
          >
            <div class="sub-style">
              <h5 class="sub-title text-primary pe-3">东南亚</h5>
              <h5>&nbsp;</h5>
            </div>
            <h2>新加坡AEIS：</h2>
            <h5>&nbsp;</h5>
            <p class="mb-5">
              即“新加坡国际学生入学考试”，面向小学二年级至初中二年级适龄国际学生提供入读新加坡政府学校的机会。
              新加坡公立教育凭借其优质的教学质量、优越的学习环境、亲民的留学费用、新加坡本地及欧美名校的升学优势，
              贴心的陪读及移民政策等要素，成为中国低龄留学生的首选之地。新辉煌国际教育与新加坡本地优质、
              声誉良好、经验丰富的AEIS培训学校/机构合作，为学生提供线上AIES考试培训。
            </p>
            <div class="col-12">
              <a
                class="btn btn-primary border-secondary rounded-pill py-3 px-5 wow fadeInUp"
                data-wow-delay="0.1s"
                href="contact.html#contact"
                >了解更多</a
              >
            </div>
          </div>

          <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 ">
            <picture>
              <source srcset="图片/国际教育-线上课程4.webp" type="image/webp">
              <img src="图片/国际教育-线上课程4.jpg" class="img-fluid" alt="" style="max-width: 100%; margin: 0% 0 0 0" />
            </picture>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact Start -->
    <div class="container-fluid contact overflow-hidden py-5" style="padding-bottom: 1rem !important">
    <div class="container py-5" style="padding-bottom: 1rem !important">
      <div class="row g-5 mb-5" style="justify-content: flex-end; margin-bottom: 0rem !important">
        <div class="d-flex border-bottom mb-4 pb-4" style="align-items: center"></div>

        <div class="col-lg-6 wow fadeInLeft" data-wow-delay="0.1s" style="max-width: 50%; margin-top: 9%">
          <div class="sub-style">
            <h5 class="sub-title text-primary pe-3">新辉煌出国</h5>
          </div>
          <h1 class="display-5 mb-4">新辉煌国际教育升学手册</h1>

          <h5>&nbsp; &nbsp;</h5>

          <p class="mb-5">
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;出国留学、培训深造、“学人之长补己之短”，是众多莘莘学子们梦寐以求的心愿。中国教育部也保持一贯支持的方针政策，明确“支持留学、鼓励回国、来去自由、发挥作用”。
          </p>
          <p class="mb-5">
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;新辉煌国际教育积极顺应社会及市场发展需求，热诚关注每一位学子的渴求，竭诚为学子们提供完善的留学服务。当下留学环境已发生巨大的变化，我们依托行业前辈十数年的专业经验和资源，提供与时俱进、全面优质的留学项目及服务。
          </p>
        </div>

        <div class="col-lg-6 wow fadeInRight" data-wow-delay="0.3 " style="text-align:center;">
          <picture>
            <source srcset="图片/新辉煌留学世界.webp" type="image/webp">
            <img src="图片/新辉煌留学世界.png" class="img-fluid" alt=""
              style="max-width:100%;height:auto;margin:0 auto;display:block;" />
          </picture>
        </div>
      </div>
    </div>
  </div>

  <!-- ========== 底部栏区块 ========== -->
  <div id="footer-placeholder"></div>
  <script>
    fetch('footer.html')
      .then(res => res.text())
      .then(html => { document.getElementById('footer-placeholder').innerHTML = html; });
  </script>
  <!-- 底部栏区块结束 -->

    <!-- ========== 侧边悬浮导航区块 ========== -->
    <div id="custom-service-placeholder"></div>
    <script>
      fetch('components/custom-service.html')
        .then(res => res.text())
        .then(html => {
          document.getElementById('custom-service-placeholder').innerHTML = html;
        });
    </script>
    <!-- 侧边悬浮导航区块结束 -->

    <!-- ========== 本地JS依赖区块 ========== -->
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/counterup/counterup.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="js/main.js"></script>
    <!-- ========== 本地JS依赖区块 End ========== -->
  </body>
</html>

