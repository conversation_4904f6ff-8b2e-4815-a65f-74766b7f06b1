(function ($) {
    "use strict";

    // Spinner
    var spinner = function () {
        setTimeout(function () {
            if ($('#spinner').length > 0) {
                $('#spinner').removeClass('show');
            }
        }, 1);
    };
    spinner(0);
    
    
    // Initiate the wowjs
    new WOW().init();


    // Sticky Navbar
    $(window).scroll(function () {
        if ($(this).scrollTop() > 45) {
            $('.nav-bar').addClass('sticky-top shadow-sm');
        } else {
            $('.nav-bar').removeClass('sticky-top shadow-sm');
        }
    });

    // Facts counter
    $('[data-toggle="counter-up"]').counterUp({
        delay: 5,
        time: 2000
    });


    // Modal Video
    $(document).ready(function () {
        var $videoSrc;
        $('.btn-play').click(function () {
            $videoSrc = $(this).data("src");
        });
        console.log($videoSrc);

        $('#videoModal').on('shown.bs.modal', function (e) {
            $("#video").attr('src', $videoSrc + "?autoplay=1&amp;modestbranding=1&amp;showinfo=0");
        })

        $('#videoModal').on('hide.bs.modal', function (e) {
            $("#video").attr('src', $videoSrc);
        })
    });


    // Testimonial-carousel
    $(".testimonial-carousel").owlCarousel({
        autoplay: true,
        smartSpeed: 2000,
        center: false,
        dots: false,
        loop: true,
        margin: 25,
        nav : true,
        navText : [
            '<i class="bi bi-arrow-left"></i>',
            '<i class="bi bi-arrow-right"></i>'
        ],
        responsiveClass: true,
        responsive: {
            0:{
                items:1
            },
            576:{
                items:1
            },
            768:{
                items:2
            },
            992:{
                items:2
            },
            1200:{
                items:2
            }
        }
    });

    
    
   // Back to top button
   $(window).scroll(function () {
    if ($(this).scrollTop() > 300) {
        $('.back-to-top').fadeIn('slow');
    } else {
        $('.back-to-top').fadeOut('slow');
    }
    });
    $('.back-to-top').click(function () {
        $('html, body').animate({scrollTop: 0}, 1500, 'easeInOutExpo');
        return false;
    });


    // 移动端下拉菜单主按钮点击拦截，禁止跳转，仅展开下级菜单
$(function () {
  function isMobileNav() {
    return window.innerWidth <= 991;
  }
  // 自动移除移动端下拉主按钮的 onclick 和 data-bs-toggle 属性，彻底禁止跳转和 Bootstrap dropdown
  function removeDropdownAttrs() {
    if (isMobileNav()) {
      $('.nav-item.dropdown > .nav-link').removeAttr('onclick').removeAttr('data-bs-toggle');
    }
  }
  removeDropdownAttrs();
  $(window).on('resize', removeDropdownAttrs);

  // 事件委托，适配所有页面
  $(document).on('click', '.nav-item.dropdown > .nav-link', function (e) {
    if (isMobileNav()) {
      var $dropdown = $(this).closest('.nav-item.dropdown');
      if ($dropdown.length) {
        e.preventDefault();
        // 延迟处理，保证 document click 先收起，再展开
        setTimeout(() => {
          var $menu = $dropdown.find('.dropdown-menu').first();
          if ($menu.is(':visible')) {
            $menu.slideUp(180);
            $dropdown.removeClass('show');
          } else {
            $('.dropdown-menu').slideUp(180); // 收起其他
            $('.nav-item.dropdown').removeClass('show');
            $menu.slideDown(180);
            $dropdown.addClass('show');
          }
        }, 0);
        return false;
      }
    }
  });
  // 点击空白处收起所有下拉（排除主按钮本身）
  $(document).on('click', function (e) {
    if (isMobileNav()) {
      if ($(e.target).closest('.nav-item.dropdown > .nav-link').length) return;
      if (!$(e.target).closest('.nav-item.dropdown').length) {
        $('.dropdown-menu').slideUp(180);
        $('.nav-item.dropdown').removeClass('show');
      }
    }
  });
  // 屏幕变化时收起所有下拉
  $(window).on('resize', function () {
    if (!isMobileNav()) {
      $('.dropdown-menu').removeAttr('style');
      $('.nav-item.dropdown').removeClass('show');
    }
  });
});


})(jQuery);

