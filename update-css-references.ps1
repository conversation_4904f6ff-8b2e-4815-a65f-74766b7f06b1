# CSS引用更新脚本
# 批量更新HTML文件中的CSS引用路径

$htmlFiles = Get-ChildItem -Path "." -Filter "*.html" | Where-Object { $_.Name -ne "footer.html" }

foreach ($file in $htmlFiles) {
    Write-Host "Processing $($file.Name)..."
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    # 更新css2路径到merged
    $content = $content -replace 'href="\.\/css\/css2\/', 'href="./css/merged/'
    $content = $content -replace 'href="css\/css2\/', 'href="css/merged/'
    
    # 更新css3路径到merged，特别处理index.css
    $content = $content -replace 'href="\.\/css\/css3\/index\.css"', 'href="./css/merged/index-modern.css"'
    $content = $content -replace 'href="css\/css3\/index\.css"', 'href="css/merged/index-modern.css"'
    
    # 移除重复的CSS引用
    # 移除重复的flag.css引用
    $lines = $content -split "`n"
    $newLines = @()
    $seenCss = @{}
    
    foreach ($line in $lines) {
        if ($line -match 'href="[^"]*/([\w\-\(\)]+\.css)"') {
            $cssFile = $matches[1]
            if (-not $seenCss.ContainsKey($cssFile)) {
                $seenCss[$cssFile] = $true
                $newLines += $line
            } else {
                Write-Host "  Removed duplicate: $cssFile"
            }
        } else {
            $newLines += $line
        }
    }
    
    $content = $newLines -join "`n"
    
    # 保存文件
    Set-Content -Path $file.FullName -Value $content -Encoding UTF8
    Write-Host "  Updated $($file.Name)"
}

Write-Host "All HTML files updated successfully!"
