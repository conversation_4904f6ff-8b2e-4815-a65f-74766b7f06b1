﻿<!-- ========== 新辉煌出国 加拿大移民 Immigration-Canada.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <!-- ========== 头部meta与样式引入区块 ========== -->
  <meta charset="utf-8" />
  <title>新辉煌出国</title>
  <meta content="width=device-width, initial-scale=1.0" name="viewport" />
  <meta content="" name="keywords" />
  <meta content="" name="description" />
  <link rel="shortcut icon" href="图片/新辉煌logo.png" />
  <!-- 本地字体与样式 -->
  <link href="css/responsive.css" rel="stylesheet" />
  <link href="css/css4/gg1.css" rel="stylesheet" />
  <link href="css/bootstrap.min.css" rel="stylesheet" />
  <link href="css/style.css" rel="stylesheet" />
  <link rel="stylesheet" href="css/fontawesome/all.css" />
  <link href="css/bootstrap-icons/bootstrap-icons.css" rel="stylesheet" />
  <link href="lib/animate/animate.min.css" rel="stylesheet" />
  <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />
  <!-- 业务专用样式 - 合并优化后 -->
  <link rel="stylesheet" href="./css/merged/reset1.css" />
  <link rel="stylesheet" href="./css/merged/common1.css" />
  <link rel="stylesheet" href="./css/merged/window1.css" />
  <link rel="stylesheet" href="./css/merged/advisers1.css" />
  <link rel="stylesheet" href="./css/merged/flag_window.css" />
  <link rel="stylesheet" href="./css/merged/flag.css" />
  <link rel="stylesheet" href="./css/merged/swiper.css" />
  <link rel="stylesheet" href="./css/merged/globe_subject.css" />
  <link rel="stylesheet" href="./css/merged/public.css" />
  <link rel="stylesheet" href="./css/merged/index_new.css" />
  <link rel="stylesheet" href="./css/merged/index-modern.css" />
  <link rel="stylesheet" href="./css/merged/project_page_cq_new.css" />
  <link rel="stylesheet" href="./css/merged/jinrErweima.css" />
  <link rel="stylesheet" href="./css/merged/flag(1).css" />
  <!-- ========== 头部meta与样式引入区块 End ========== -->
</head>
<style>
  .Breadcrumbs{
    height:auto !important;
  }
</style>
<body>
  <!-- ========== 顶部加载动画区块 ========== -->
  <!-- Spinner加载动画 -->
  <div id="spinner"
    class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
    <div class="spinner-border text-secondary" style="width: 3rem; height: 3rem" role="status">
      <span class="sr-only">加载中...</span>
    </div>
  </div>
  <!-- Spinner End -->
  <!-- ========== 顶部加载动画区块 End ========== -->

  <!-- ========== 顶部栏区块 ========== -->
  <!-- 顶部栏（Topbar） -->
  <div class="container-fluid bg-primary px-5 d-none d-lg-block">
    <div class="row gx-0 align-items-center">
      <div class="col-lg-5 text-center text-lg-start mb-lg-0">
        <div class="d-flex"></div>
      </div>
      <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
        <div class="d-inline-flex align-items-center" style="height: 45px"></div>
      </div>
      <div class="col-lg-4 text-center text-lg-end">
        <div class="d-inline-flex align-items-center" style="height: 45px"></div>
      </div>
    </div>
  </div>
  <!-- Topbar End -->
  <!-- ========== 顶部栏区块 End ========== -->

  <!-- ========== 导航栏区块 ========== -->
  <!-- 导航栏（Navbar） -->
  <div class="container-fluid nav-bar p-0">
    <nav class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0">
      <a href="" class="navbar-brand p-0">
        <h1 class="display-5 text-secondary m-0">
          <picture>
            <source srcset="图片/logo.webp" type="image/webp" />
            <img src="图片/logo.png" class="img-fluid" alt="" style="max-height: 80px" />
          </picture>
        </h1>
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
        <span class="fa fa-bars"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarCollapse">
        <div class="navbar-nav ms-auto py-0">
          <a href="index.html" class="nav-item nav-link">首页</a>
          <a href="about.html" class="nav-item nav-link">关于我们</a>
          <div class="nav-item dropdown">
            <a href="Immigration.html" class="nav-link active" data-bs-toggle="dropdown"
              onclick="window.location.href='Immigration.html'"><span class="dropdown-toggle">移民项目</span></a>
            <div class="dropdown-menu m-0">
              <a href=" Immigration-USA.html" class="dropdown-item">美国</a>
              <a href="Immigration-Canada.html" class="dropdown-item active">加拿大</a>
              <a href="Immigration-Ireland.html" class="dropdown-item">爱尔兰</a>
              <a href="Immigration-HongKong.html" class="dropdown-item">香港</a>
              <a href="Immigration-SingaporeEP.html" class="dropdown-item">新加坡</a>
              <a href="Immigration-Grenada.html" class="dropdown-item">格林纳达</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#jiaoyu'"><span class="dropdown-toggle">国际教育</span></a>
            <div class="dropdown-menu m-0">
              <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item">留学申请</a>
              <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item">线上课程</a>
              <a href="InternationalEducation-TopSchools.html" class="dropdown-item">名校直通车计划</a>
              <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item">语言课程</a>
              <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item">游学、特色团</a>
              <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item">国际学校</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#haiwai'"><span class="dropdown-toggle">海外置业</span></a>
            <div class="dropdown-menu m-0">
              <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
              <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
              <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
              <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown"
              onclick="window.location.href='index.html#pingtai'"><span class="dropdown-toggle">平台合作</span></a>
            <div class="dropdown-menu m-0">
              <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item">合和法律平台</a>
              <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item">精英留学平台</a>
              <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item">新辉煌金融平台</a>
            </div>
          </div>
          <a href="contact.html" class="nav-item nav-link">联系我们</a>
        </div>
      </div>
    </nav>
  </div>
  <!-- Navbar End -->
  <!-- ========== 导航栏区块 End ========== -->

  <!-- ========== 头图区块 ========== -->
  <!-- 面包屑/头图（Header） -->
  <div class="container-fluid bg-breadcrumb size-breadcrumb"
    style="background: linear-gradient(rgba(0, 58, 102, 0), rgba(0, 58, 102, 0)), url(./图片/头图-加拿大.jpg); background-position: center center; background-repeat: no-repeat; background-attachment: initial; background-size: contain; padding: 394px 0 60px 0;">
    <div class="container text-center py-5" style="max-width: 900px">
      <h3 class="text-white display-3 mb-4 wow fadeInDown" data-wow-delay="0.1s"></h3>
      <ol class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown" data-wow-delay="0.3s">
        <li class="breadcrumb-item">
          <a href="index.html" class="text-white"></a>
        </li>
      </ol>
    </div>
  </div>
  <!-- Header End -->
  <!-- ========== 头图区块 End ========== -->

  <!-- ========== 搜索弹窗区块 ========== -->
  <!-- 搜索弹窗 -->
  <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
      <div class="modal-content rounded-0">
        <div class="modal-header">
          <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">搜索</h4>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body d-flex align-items-center">
          <div class="input-group w-75 mx-auto d-flex">
            <input type="search" class="form-control p-3" placeholder="输入关键词搜索" aria-describedby="search-icon-1" />
            <span id="search-icon-1" class="input-group-text p-3"><i class="fa fa-search"></i></span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 搜索弹窗 End -->
  <!-- ========== 搜索弹窗区块 End ========== -->

  <!-- ========== 主体内容区块 ========== -->
  <!-- 主体内容（加拿大SUV项目/流程/优势等） -->
  <!-- 头部 -->
  <div class="Breadcrumbs">
    <div class="width">
      <h3>加拿大创新企业家移民 (SUV)</h3>
      <div class="b-content clearfix" style="position: relative">
        <div class="pull-left b-left">
          <p class="two">
            加拿大创新企业家移民（SUV）是加拿大联邦政府于2013推出的移民项目。
            SUV是申请人凭借商业想法，获得指定机构的支持信，可以一步到位申请永居的联邦移民项目。
            审批期间客户全程无需登陆，整体周期2-3年即可获得枫叶卡。
          </p>

          <div style="display: flex; flex-wrap: nowrap">
            <a href="contact.html#contact" class="pmq text-right"
              style="display: block; margin: 42px 100px 0 0">在线咨询</a>
            <a href="contact.html#contact" class="pmq text-right"
              style="display: block; width: 132px; height: 38px; color: #fff; background-color: #a67e3d; line-height: 38px; font-size: 16px; margin-top: 42px; border-radius: 19px; text-align: center;">联系我们</a>
          </div>
        </div>
        <div class="pull-left b-right gs_office_imgs">
          <picture>
            <source srcset="./css/css2/img/加拿大移民2.webp" type="image/webp" />
            <img src="./css/css2/img/加拿大移民2.jpg" style="display: block" />
          </picture>

          <!-- <img src="/static/index/images/project/b-right-video-btn.png" alt="" class="b-right-video-btn"> -->
          <div class="b-right-items">
            <div class="b-right-items-info">
              <img style="width: 24px; height: 21px; border-radius: unset; display: block;"
                data-original="css/css2/img/project_blzq.png" src="./css/css2/img/project_blzq.png" alt="" />
              <p>办理周期</p>
              <p>1.5-2年</p>
            </div>
            <div class="b-right-items-info">
              <img style="width: 34px; height: 20px; border-radius: unset; display: block;"
                data-original="css/css2/img/project_hdsf.png" src="./css/css2/img/project_hdsf.png" alt="" />
              <p>获得身份</p>
              <p>永居（绿卡）</p>
            </div>
            <div class="b-right-items-info">
              <img style="width: 24px; height: 21px; border-radius: unset; display: block;"
                data-original="css/css2/img/project_zcyq.png" src="./css/css2/img/project_zcyq.png" alt="" />
              <p>资产要求</p>
              <p>20万加币</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 申请条件 -->
  <div class="Breadcrumbs">
    <div class="width">
      <h3>加拿大创新企业家移民(SUV)申请条件</h3>
      <div class="b-content clearfix" style="position: relative">
        <div class="pull-left b-left">
          <p class="two">
            申请人拥有20万加币，或等值对应资金的银行流水余额、存款证明;已经拥有超过雅思5分的成绩单，
            单项至少为5545(或者曾经拥有高于此水平的雅思成绩);申请人能够开具无犯罪记录证明(居住满6个月及以上的全部国家)。
          </p>
          <!-- <a href="/ympg.html" target="_blank" class="text-center" style="display:block">在线咨询</a> -->
          <!-- <a target="_blank" class="pmq text-right" style="position: absolute;bottom: 0px;right: 70px;" href="javascript:;" onclick="parent.openMeiqia(null,'Xiangmuye','5',2)" >直接咨询专家</a> -->
          <div style="display: flex; flex-wrap: nowrap">
            <a href="contact.html#contact" class="pmq text-right"
              style="display: block; margin: 42px 100px 0 0">在线咨询</a>
            <a href="contact.html#contact" class="pmq text-right"
              style="display: block; width: 132px; height: 38px; color: #fff; background-color: #a67e3d; line-height: 38px; font-size: 16px; margin-top: 42px; border-radius: 19px; text-align: center;">联系我们</a>
          </div>
        </div>
        <div class="pull-left b-right gs_office_imgs">
          <picture>
            <source srcset="./css/css2/img/加拿大移民1.webp" type="image/webp" />
            <img src="./css/css2/img/加拿大移民1.jpg" style="display: block" />
          </picture>
          <!-- <img src="/static/index/images/project/b-right-video-btn.png" alt="" class="b-right-video-btn"> -->
        </div>
      </div>
    </div>
  </div>

  <!--加拿大创新企业家移民流程 组件化引入区块 -->
  <div id="process-canada-container"></div>
  <script>
    fetch('components/process-canada.html')
      .then(res => res.text())
      .then(html => {
        document.getElementById('process-canada-container').innerHTML = html;
      });
  </script>

  <!-- 购房移民申请条件 -->
  <div class="application">
    <div class="width">
      <div class="conditions clearfix">
        <div class="pull-left conditions-left">
          <!-- <a href="/ympg.html" target="_blank"> -->
          <picture>
            <source srcset="./css/css2/img/加拿大移民3.webp" type="image/webp" />
            <img src="./css/css2/img/加拿大移民3.jpg" />
          </picture>
          <!-- </a> -->
        </div>
        <div class="pull-right conditions-right">
          <h3>加拿大创新企业家移民(SUV)为什么选择新辉煌?<br />&nbsp;</h3>
          <h4>完善的产品线让您选择无忧<br />&nbsp;</h4>
          <h4>项目开发团队让您放心无忧<br />&nbsp;</h4>
          <h4>线上化服务让您沟通无忧<br />&nbsp;</h4>
          <h4>良好政府关系让您递交无忧<br />&nbsp;</h4>
        </div>
      </div>
    </div>
  </div>

  <h2>
    &nbsp;<br />
    &nbsp;<br />
  </h2>
  <!-- ========== 主体内容区块 End ========== -->

  <!-- ========== 底部栏区块 ========== -->
  <!-- 底部栏（Footer，动态引入） -->
  <div id="footer-placeholder"></div>
  <script>
    fetch('footer.html')
      .then(res => res.text())
      .then(html => { document.getElementById('footer-placeholder').innerHTML = html; });
  </script>
  <!-- Footer End -->
  <!-- ========== 底部栏区块 End ========== -->

  <!-- ========== 侧边悬浮导航区块 ========== -->
  <div id="custom-service-placeholder"></div>
  <script>
    fetch('components/custom-service.html')
      .then(res => res.text())
      .then(html => {
        document.getElementById('custom-service-placeholder').innerHTML = html;
      });
  </script>
  <!-- 侧边悬浮导航区块 End ========== -->

  <!-- ========== 本地JS依赖区块 ========== -->
  <!-- 脚本引入 -->
  <script src="js/jquery-3.6.4.min.js"></script>
  <script src="js/bootstrap.bundle.min.js"></script>
  <script src="lib/wow/wow.min.js"></script>
  <script src="lib/easing/easing.min.js"></script>
  <script src="lib/waypoints/waypoints.min.js"></script>
  <script src="lib/counterup/counterup.min.js"></script>
  <script src="lib/owlcarousel/owl.carousel.min.js"></script>
  <script src="js/main.js"></script>
  <!-- ========== 本地JS依赖区块 End ========== -->
</body>

</html>
