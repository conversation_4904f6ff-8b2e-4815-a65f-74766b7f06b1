﻿<!-- ========== 新辉煌出国 关于我们 about.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <!-- ========== 头部meta与样式引入区块 ========== -->
    <meta charset="utf-8" />
    <title>新辉煌出国</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="" name="keywords" />
    <meta content="" name="description" />
    <!-- 本地字体与样式 -->
    <link href="css/responsive.css" rel="stylesheet" />
    <link rel="stylesheet" href="css/fontawesome/all.css" />
    <link href="css/bootstrap-icons/bootstrap-icons.css" rel="stylesheet" />
    <link href="lib/animate/animate.min.css" rel="stylesheet" />
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />
    <link href="css/bootstrap.min.css" rel="stylesheet" />
    <link href="css/style.css" rel="stylesheet" />
    <link href="css/css4/gg1.css" rel="stylesheet" />
    <style>
      .hover:hover {
        color: var(--bs-secondary) !important;
      }
    </style>
    <link rel="shortcut icon" href="图片/新辉煌logo.png" />
    <!-- ========== 头部meta与样式引入区块 End ========== -->
  </head>

  <body>
    <!-- ========== 顶部加载动画区块 ========== -->
    <div
      id="spinner"
      class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center"
    >
      <div
        class="spinner-border text-secondary"
        style="width: 3rem; height: 3rem"
        role="status"
      >
        <span class="sr-only">Loading...</span>
      </div>
    </div>
    <!-- Spinner End -->
    <!-- ========== 顶部加载动画区块 End ========== -->

    <!-- ========== 顶部栏区块 ========== -->
    <div class="container-fluid bg-primary px-5 d-none d-lg-block">
      <div class="row gx-0 align-items-center">
        <div class="col-lg-5 text-center text-lg-start mb-lg-0">
          <div class="d-flex"></div>
        </div>
        <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
        <div class="col-lg-4 text-center text-lg-end">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
      </div>
    </div>
    <!-- Topbar End -->
    <!-- ========== 顶部栏区块 End ========== -->

    <!-- ========== 导航栏区块 ========== -->
    <div class="container-fluid nav-bar p-0">
      <nav
        class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0"
      >
        <a href="" class="navbar-brand p-0">
          <h1 class="display-5 text-secondary m-0">
            <img
              src="图片/logo.png"
              class="img-fluid"
              alt=""
              style="max-height: 80px"
            />
          </h1>
          <!-- <img src="img/logo.png" alt="Logo"> -->
        </a>
        <button
          class="navbar-toggler"
          type="button"
          data-bs-toggle="collapse"
          data-bs-target="#navbarCollapse"
        >
          <span class="fa fa-bars"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarCollapse">
          <div class="navbar-nav ms-auto py-0">
            <a href="index.html" class="nav-item nav-link">首页</a>
            <a href="about.html" class="nav-item nav-link active">关于我们</a>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='Immigration.html'"
                ><span class="dropdown-toggle">移民项目</span></a
              >
              <div class="dropdown-menu m-0">
                <a href=" Immigration-USA.html" class="dropdown-item">美国</a>
                <a href="Immigration-Canada.html" class="dropdown-item">加拿大</a>
                <a href="Immigration-Ireland.html" class="dropdown-item">爱尔兰</a>
                <a href="Immigration-HongKong.html" class="dropdown-item">香港</a>
                <a href="Immigration-SingaporeEP.html" class="dropdown-item"
                  >新加坡</a
                >
                <a href="Immigration-Grenada.html" class="dropdown-item"
                  >格林纳达</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#jiaoyu'"
                ><span class="dropdown-toggle">国际教育</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item"
                  >留学申请</a
                >
                <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item"
                  >线上课程</a
                >
                <a href="InternationalEducation-TopSchools.html" class="dropdown-item"
                  >名校直通车计划</a
                >
                <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item"
                  >语言课程</a
                >
                <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item"
                  >游学、特色团</a
                >
                <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item"
                  >国际学校</a
                >
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#haiwai'"
                ><span class="dropdown-toggle">海外置业</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
                <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
                <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
                <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
              </div>
            </div>
            <div class="nav-item dropdown">
              <a
                href="#"
                class="nav-link"
                data-bs-toggle="dropdown"
                onclick="window.location.href='index.html#pingtai'"
                ><span class="dropdown-toggle">平台合作</span></a
              >
              <div class="dropdown-menu m-0">
                <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item"
                  >合和法律平台</a
                >
                <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item"
                  >精英留学平台</a
                >
                <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item"
                  >新辉煌金融平台</a
                >
              </div>
            </div>
            <a href="contact.html" class="nav-item nav-link">联系我们</a>
          </div>
        </div>
      </nav>
    </div>
    <!-- Navbar & Hero End -->
    <!-- ========== 导航栏区块 End ========== -->

    <!-- ========== 头图区块 ========== -->
    <div class="container-fluid bg-breadcrumb">
      <div class="container text-center py-5" style="max-width: 900px">
        <h3
          class="text-white display-3 mb-4 wow fadeInDown"
          data-wow-delay="0.1s"
        >
          关于我们
        </h3>
        <ol
          class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown"
          data-wow-delay="0.3s"
        >
          <li class="breadcrumb-item">
            <a href="index.html" class="text-white">主页</a>
          </li>

          <li class="breadcrumb-item active text-secondary">关于我们</li>
        </ol>
      </div>
    </div>
    <!-- Header End -->
    <!-- ========== 头图区块 End ========== -->

    <!-- ========== 搜索弹窗区块 ========== -->
    <div
      class="modal fade"
      id="searchModal"
      tabindex="-1"
      aria-labelledby="exampleModalLabel"
      aria-hidden="true"
    >
      <div class="modal-dialog modal-fullscreen">
        <div class="modal-content rounded-0">
          <div class="modal-header">
            <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">
              搜索
            </h4>
            <button
              type="button"
              class="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            ></button>
          </div>
          <div class="modal-body d-flex align-items-center">
            <div class="input-group w-75 mx-auto d-flex">
              <input
                type="search"
                class="form-control p-3"
                placeholder="输入关键词搜索"
                aria-describedby="search-icon-1"
              />
              <span id="search-icon-1" class="input-group-text p-3"
                ><i class="fa fa-search"></i
              ></span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Modal Search End -->
    <!-- ========== 搜索弹窗区块 End ========== -->

    <!-- ========== 关于我们主体区块 ========== -->
    <div class="container-fluid py-5">
      <div class="container py-5">
        <div class="row g-5">
          <div class="col-xl-5 wow fadeInLeft" data-wow-delay="0.1s">
            <div class="bg-light rounded">
              <img
                src="img/about-2.png"
                class="img-fluid w-100"
                style="margin-bottom: -7px"
                alt="Image"
              />
              <img
                src="img/about-3.jpg"
                class="img-fluid w-100 border-bottom border-5 border-primary"
                style="
                  border-top-right-radius: 300px;
                  border-top-left-radius: 300px;
                "
                alt="Image"
              />
            </div>
          </div>
          <div class="col-xl-7 wow fadeInRight" data-wow-delay="0.3s">
            <h5 class="sub-title pe-3">关于公司</h5>
            <h1 class="display-5 mb-4">我们是值得信赖的出国项目咨询机构</h1>
            <p class="mb-4">
              <b>“出国找辉煌，一站通全球”。</b
              >新辉煌出国一直致力于为中国高净值人士提供海外需求解决方案。包括海外身份规划、子女国际教育、海外安家置业、企业出海上市等跨境服务咨询。
            </p>
            <div class="row gy-4 align-items-center">
              <div class="col-12 col-sm-6 d-flex align-items-center">
                <i class="fas fa-passport fa-3x text-secondary"></i>
                <h5 class="ms-4">优质海外源头资源</h5>
              </div>

              <div class="col-12 col-sm-6 d-flex align-items-center">
                <i class="fas fa-shield-alt fa-3x text-secondary"></i>
                <h5 class="ms-4">不成功咨询费全退</h5>
              </div>

              <div class="col-4 col-md-3">
                <div class="bg-light text-center rounded p-3">
                  <div class="mb-2"></div>
                  <h1 class="display-5 fw-bold mb-2 text-secondary2">49年</h1>
                  <p class="text-muted mb-0">行业经验</p>
                </div>
              </div>
              <div class="col-8 col-md-9">
                <div class="mb-5">
                  <p class="text-primary h6 mb-3">
                    <i class="fa fa-check-circle text-secondary me-2"></i>
                    多元服务：移民、留学、海外置业、企业出海
                  </p>
                  <p class="text-primary h6 mb-3">
                    <i class="fa fa-check-circle text-secondary me-2"></i>
                    源头项目：海外源头项目资源，中外直营
                  </p>
                  <p class="text-primary h6 mb-3">
                    <i class="fa fa-check-circle text-secondary me-2"></i>
                    安全保障：资金监管、法律护航，不成功咨询费全退
                  </p>
                </div>
                <div class="d-flex flex-wrap">
                  <div
                    id="phone-tada"
                    class="d-flex align-items-center justify-content-center me-4"
                  >
                    <a
                      href=""
                      class="position-relative wow tada"
                      data-wow-delay=".9s"
                    >
                      <i class="fab fa-weixin text-primary fa-3x"></i>
                    </a>
                  </div>

                  <div class="d-flex flex-column justify-content-center">
                    <span class="text-primary">有任何问题吗？</span>
                    <span
                      class="text-secondary fw-bold fs-5"
                      style="letter-spacing: 2px"
                      >扫描二维码咨询我们:</span
                    >
                  </div>

                  <div class="d-flex flex-column justify-content-center">
                    <img
                      src="图片/二维码.jpg"
                      class=""
                      alt=""
                      style="max-height: 80px"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- About End -->
    <!-- ========== 关于我们主体区块 End ========== -->

    <!-- ========== 核心优势区块 ========== -->
    <div class="container-fluid counter-facts py-5">
      <div id="counter-features-container"></div>
    </div>
    <script>
      fetch("components/counter-features.html")
        .then((res) => res.text())
        .then((html) => {
          document.getElementById("counter-features-container").innerHTML =
            html;
        });
    </script>
    <!-- ========== 核心优势区块 End ========== -->

    <!-- ========== 底部栏区块 ========== -->
    <div id="footer-placeholder" class="aboout-py-5">
    <script>
      fetch("footer.html")
        .then((res) => res.text())
        .then((html) => {
          document.getElementById("footer-placeholder").innerHTML = html;
        });
    </script>
    </div>
    <!-- Footer end -->
    <!-- ========== 底部栏区块 End ========== -->

    <!-- ========== 悬浮客服区块 ========== -->
    <div id="custom-service-placeholder" class="about-kf-5">
    <script>
      fetch("components/custom-service.html")
        .then((res) => res.text())
        .then((html) => {
          document.getElementById("custom-service-placeholder").innerHTML =
            html;
        });
    </script>
    </div>
    <!-- ========== 悬浮客服区块 End ========== -->

    <!-- ========== 本地JS依赖区块 ========== -->
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/counterup/counterup.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="js/main.js"></script>
    <!-- ========== 本地JS依赖区块 End ========== -->
  </body>
</html>

