﻿<!-- ========== 新辉煌出国 美国移民  Immigration-USA.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">

<head>
  <!-- ========== 头部meta与样式引入区块 ========== -->
  <meta charset="utf-8" />
  <title>新辉煌出国</title>
  <meta content="width=device-width, initial-scale=1.0" name="viewport" />
  <meta content="" name="keywords" />
  <meta content="" name="description" />
  <link rel="shortcut icon" href="图片/新辉煌logo.png" />
  <!-- 本地字体与样式 -->
  <link href="css/responsive.css" rel="stylesheet" />
  <link href="css/css4/gg1.css" rel="stylesheet" />
  <link href="css/bootstrap.min.css" rel="stylesheet" />
  <link href="css/style.css" rel="stylesheet" />
  <link rel="stylesheet" href="css/fontawesome/all.css" />
  <link href="css/bootstrap-icons/bootstrap-icons.css" rel="stylesheet" />
  <link href="lib/animate/animate.min.css" rel="stylesheet" />
  <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />
  <!-- 业务专用样式 -->
  <link rel="stylesheet" href="./css/merged/reset1.css" />
  <link rel="stylesheet" href="./css/merged/common1.css" />
  <link rel="stylesheet" href="./css/merged/window1.css" />
  <link rel="stylesheet" href="./css/merged/advisers1.css" />
  <link rel="stylesheet" href="./css/merged/flag_window.css" />
  <link rel="stylesheet" href="./css/merged/flag.css" />
  <link rel="stylesheet" href="./css/merged/swiper.css" />
  <link rel="stylesheet" href="./css/merged/globe_subject.css" />
  <link rel="stylesheet" href="./css/merged/public.css" />
  <link rel="stylesheet" href="./css/merged/index_new.css" />
  <link rel="stylesheet" href="./css/merged/index-modern.css" />
  <link rel="stylesheet" href="./css/merged/project_page_cq_new.css" />
  <link rel="stylesheet" href="./css/merged/jinrErweima.css" />
  <link rel="stylesheet" href="./css/merged/flag(1).css" />
  <!-- ========== 头部meta与样式引入区块 End ========== -->
</head>

<body>
  <!-- ========== 顶部加载动画区块 ========== -->
  <!-- Spinner加载动画 -->
  <div id="spinner"
    class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
    <div class="spinner-border text-secondary" style="width: 3rem; height: 3rem" role="status">
      <span class="sr-only">加载中...</span>
    </div>
  </div>
  <!-- Spinner End -->
  <!-- ========== 顶部加载动画区块 End ========== -->

  <!-- ========== 顶部栏区块 ========== -->
  <!-- 顶部栏（Topbar） -->
  <div class="container-fluid bg-primary px-5 d-none d-lg-block">
    <div class="row gx-0 align-items-center">
      <div class="col-lg-5 text-center text-lg-start mb-lg-0">
        <div class="d-flex"></div>
      </div>
      <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
        <div class="d-inline-flex align-items-center" style="height: 45px"></div>
      </div>
      <div class="col-lg-4 text-center text-lg-end">
        <div class="d-inline-flex align-items-center" style="height: 45px"></div>
      </div>
    </div>
  </div>
  <!-- Topbar End -->
  <!-- ========== 顶部栏区块 End ========== -->

  <!-- ========== 导航栏区块 ========== -->
  <!-- 导航栏（Navbar） -->
  <div class="container-fluid nav-bar p-0">
    <nav class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0">
      <a href="" class="navbar-brand p-0">
        <h1 class="display-5 text-secondary m-0">
          <picture>
            <source srcset="图片/logo.webp" type="image/webp" />
            <img src="图片/logo.png" class="img-fluid" alt="" style="max-height: 80px" />
          </picture>
        </h1>
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse">
        <span class="fa fa-bars"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarCollapse">
        <div class="navbar-nav ms-auto py-0">
          <a href="index.html" class="nav-item nav-link">首页</a>
          <a href="about.html" class="nav-item nav-link">关于我们</a>
          <div class="nav-item dropdown">
            <a href="Immigration.html" class="nav-link active" data-bs-toggle="dropdown" onclick="window.location.href='Immigration.html'"><span class="dropdown-toggle">移民项目</span></a>
            <div class="dropdown-menu m-0">
              <a href=" Immigration-USA.html" class="dropdown-item active">美国</a>
              <a href="Immigration-Canada.html" class="dropdown-item">加拿大</a>
              <a href="Immigration-Ireland.html" class="dropdown-item">爱尔兰</a>
              <a href="Immigration-HongKong.html" class="dropdown-item">香港</a>
              <a href="Immigration-SingaporeEP.html" class="dropdown-item">新加坡</a>
              <a href="Immigration-Grenada.html" class="dropdown-item">格林纳达</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#jiaoyu'"><span class="dropdown-toggle">国际教育</span></a>
            <div class="dropdown-menu m-0">
              <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item">留学申请</a>
              <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item">线上课程</a>
              <a href="InternationalEducation-TopSchools.html" class="dropdown-item">名校直通车计划</a>
              <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item">语言课程</a>
              <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item">游学、特色团</a>
              <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item">国际学校</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#haiwai'"><span class="dropdown-toggle">海外置业</span></a>
            <div class="dropdown-menu m-0">
              <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
              <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
              <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
              <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
            </div>
          </div>
          <div class="nav-item dropdown">
            <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#pingtai'"><span class="dropdown-toggle">平台合作</span></a>
            <div class="dropdown-menu m-0">
              <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item">合和法律平台</a>
              <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item">精英留学平台</a>
              <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item">新辉煌金融平台</a>
            </div>
          </div>
          <a href="contact.html" class="nav-item nav-link">联系我们</a>
        </div>
      </div>
    </nav>
  </div>
  <!-- Navbar End -->
  <!-- ========== 导航栏区块 End ========== -->

  <!-- ========== 头图区块 ========== -->
  <!-- 面包屑/头图（Header） -->
  <div class="container-fluid bg-breadcrumb size-breadcrumb" style="
        background: linear-gradient(rgba(0, 58, 102, 0), rgba(0, 58, 102, 0)),
          url(./图片/头图-美国.jpg);
        background-position: center center;
        background-repeat: no-repeat;
        background-attachment: initial;
        background-size: contain;
        padding: 350px 0 60px 0;
      ">
    <div class="container text-center py-5" style="max-width: 900px">
      <h3 class="text-white display-3 mb-4 wow fadeInDown" data-wow-delay="0.1s"></h3>

      <ol class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown" data-wow-delay="0.3s">
        <li class="breadcrumb-item">
          <a href="index.html" class="text-white"></a>
        </li>
      </ol>
    </div>
  </div>
  <!-- Header End -->
  <!-- ========== 头图区块 End ========== -->

  <!-- ========== 搜索弹窗区块 ========== -->
  <!-- 搜索弹窗 -->
  <div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen">
      <div class="modal-content rounded-0">
        <div class="modal-header">
          <h4 class="modal-title text-secondary mb-0" id="exampleModalLabel">
            搜索
          </h4>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body d-flex align-items-center">
          <div class="input-group w-75 mx-auto d-flex">
            <input type="search" class="form-control p-3" placeholder="输入关键词搜索" aria-describedby="search-icon-1" />
            <span id="search-icon-1" class="input-group-text p-3"><i class="fa fa-search"></i></span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 搜索弹窗 End -->
  <!-- ========== 搜索弹窗区块 End ========== -->

  <!-- ========== 主体内容区块 ========== -->
  <!-- 主体内容（美国EB-5项目/流程/优势等） -->
  <div class="c_project">
    <div class="main">
      <div class="mainInfo">
        <h1 class="hidden_title" style="color: black; padding-top: 50px; padding-bottom: 20px">
          新辉煌美国移民项目
        </h1>

        <h1 class="hidden_title" style="
              background-color: rgba(0, 0, 0, 0.3);
              padding-top: 0px;
              padding-bottom: 0px;
              font-size: 0.1rem;
            ">
          &nbsp;&nbsp;
        </h1>

        <div class="Breadcrumbs Breadcrumbs-height">
          <div class="width">
            <h3>美国EB-5投资移民</h3>
            <div class="b-content clearfix" style="position: relative">
              <div class="pull-left b-left">
                <p class="two">
                  EB-5投资移民项目，是由美国国会于1990年颁布的一项移民签证计划，属于美国职业移民的第五种优先签证类别。
                  2022年颁布的新政要求，申请人将80万美金投入美国政府批准的商业项目并创造10个新就业岗位，申请人及其配偶和21岁以下未婚子女可申请美国绿卡。
                </p>

                <div style="display: flex; flex-wrap: nowrap">
                  <a href="contact.html#contact" class="pmq text-right"
                    style="display: block; margin: 42px 100px 0 0">在线咨询</a>
                </div>
              </div>
              <div class="pull-left b-right gs_office_imgs">
                <picture>
                  <source srcset="./img/America_right.webp" type="image/webp" />
                  <img src="./img/America_right.png" style="display: block" />
                </picture>

                <!-- <img src="/static/index/images/project/b-right-video-btn.png" alt="" class="b-right-video-btn"> -->
                <div class="b-right-items b-right-items-1">
                  <div class="b-right-items-info">
                    <picture>
                      <source srcset="./css/css2/img/project_blzq.webp" type="image/webp" />
                      <img style="
                          width: 24px;
                          height: 21px;
                          border-radius: unset;
                          display: block;
                        " data-original="css/css2/img/project_blzq.png" src="./css/css2/img/project_blzq.png" alt="" />
                    </picture>
                    <p>办理周期</p>
                    <p>2-3年</p>
                  </div>
                  <div class="b-right-items-info">
                    <picture>
                      <source srcset="./css/css2/img/project_hdsf.webp" type="image/webp" />
                      <img style="
                          width: 34px;
                          height: 20px;
                          border-radius: unset;
                          display: block;
                        " data-original="css/css2/img/project_hdsf.png" src="./css/css2/img/project_hdsf.png" alt="" />
                    </picture>
                    <p>获得身份</p>
                    <p>永居（绿卡）</p>
                  </div>
                  <div class="b-right-items-info">
                    <picture>
                      <source srcset="./css/css2/img/project_zcyq.webp" type="image/webp" />
                      <img style="
                          width: 24px;
                          height: 21px;
                          border-radius: unset;
                          display: block;
                        " data-original="css/css2/img/project_zcyq.png" src="./css/css2/img/project_zcyq.png" alt="" />
                    </picture>
                    <p>资产要求</p>
                    <p>80万美元以上</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 购房移民申请条件 -->
        <div class="application application_eb5">
          <div class="width">
            <div class="conditions clearfix">
              <div class="pull-left conditions-left">
                <picture>
                  <source srcset="./img/America_left.webp" type="image/webp" />
                  <img src="./img/America_left.png" />
                </picture>
              </div>
              <div class="pull-right conditions-right">
                <h3>美国EB-5投资移民申请条件</h3>
                <p>
                  ●投资80万美金到合资格项目 <br />●至少创造10个就业岗位
                  <br />●投资资金来源合理合法
                  <br />●申请人通过体检并且无犯罪记录
                </p>
              </div>
            </div>
          </div>
        </div>
        <!-- EB-5投资移民申请流程 组件化引入区块 -->
        <div id="process-eb5-container"></div>
        <script>
          fetch('components/process-eb5.html')
            .then(res => res.text())
            .then(html => {
              document.getElementById('process-eb5-container').innerHTML = html;
            });
        </script>
        <div class="special_recommend_info_with">
          <div class="special_recommend_title">
            新辉煌出国 • 独家EB-5直投项目
          </div>
          <section id="specal-1" class="project_advantage_1 page_center move project_s0 special_recommend_item caonima"
            name="0" style="width: 1200px">
            <div style="
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                ">
              <div class="special_recommend_item_l">
                <p class="special_recommend_item_l_title">
                  加州五星美食餐车项目
                </p>
                <div class="special_recommend_item_l_detail">
                  <div class="icon_des">
                    <p>项目性质 : 乡村TEA</p>
                  </div>
                  <div class="icon_des">
                    <p>所在区域：美国加州</p>
                  </div>
                  <div class="icon_des">
                    <p>EB-5资金用途：股权投资</p>
                  </div>
                  <div class="icon_des">
                    <p>开工周期：约3个月</p>
                  </div>
                </div>
                <p class="special_recommend_item_l_title">项目优势</p>
                <div class="special_recommend_item_l_ystitle">
                  餐车是美国社会重要的文化标签，是不可或缺的餐饮生态元素。“美食餐车车队”EB-5直投项目，特别适合想要主动把控80万美金EB-5投资资金安全的投资者，适合计划在美国扎根、追求资金安全与绿卡平衡的投资者。它不仅能以较低成本撬动移民身份，更能通过灵活运营积累实际商业经验。在新政窗口期（预留签证充足且无排期），“美食餐车车队”绝对不失为“美国绿卡+商业收益”一举两得的优质EB-5投资选择。
                </div>
                <div class="special_recommend_item_l_yscontent">
                  <ul>

                    <li>
                      一、核心优势：低成本、高灵活性、强就业可控性;<br>
                      1. 投资门槛低、资金压力小，回本周期快；<br>
                      2. 就业创造轻松达成，绿卡有保障；<br>
                      3. 轻资产运营，运营灵活、抗风险能力强。<br>
                    </li>
                    <li>
                      二、新政红利：审批加速、身份灵活；<br>
                      1. TEA乡村地区，免排期+快速审批通道；<br>
                      2. 双递交政策锁定在美身份。<br>
                    </li>
                    <li>
                      三、与区域中心模式对比：直控权与透明度；<br>
                      1. 直接管理符合移民局“主动经营”要求；<br>
                      2. 资金流向透明，还款路径清晰；<br>
                      3. 投资人绝对控股，80万本金安全有保障。<br>
                    </li>
                    <li>
                      四、费用优势：移民成本低<br>
                      1. TEA区域项目，仅投资80万美金；<br>
                      2. 直投项目，无需区域中心项目管理费。<br>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="special_recommend_item_r">
                <picture>
                  <source srcset="./img/car1.webp" type="image/webp" />
                  <img src="./img/car1.png" alt="" />
                </picture>
              </div>
            </div>
            <section class="popular-project ke_xs_z clearfix">
              <div class="width">
                <div class="ke_xs_w project_lunbo_video gs_office_imgs" style="
                      position: relative;
                      overflow: hidden;
                      width: 100%;
                      height: auto;
                      padding: 20px 0;
                    ">
                  <div class="ke_xss ke_xs_p0" style="width: 100%; height: 100%">
                    <div class="swiper-wrapper">
                      <div class="swiper-slide" style="width: 279.333px; margin-right: 28px">
                        <picture>
                          <source srcset="./img/car2.webp" type="image/webp" />
                          <img src="./img/car2.jpg" alt="美食餐车" style="
                              width: 100%;
                              height: 200px;
                              object-fit: cover;
                            " />
                        </picture>
                      </div>
                      <div class="swiper-slide" style="width: 279.333px; margin-right: 28px">
                        <picture>
                          <source srcset="./img/car3.webp" type="image/webp" />
                          <img src="./img/car3.jpg" alt="美食餐车" style="
                              width: 100%;
                              height: 200px;
                              object-fit: cover;
                            " />
                        </picture>
                      </div>
                      <div class="swiper-slide" style="width: 279.333px; margin-right: 28px">
                        <picture>
                          <source srcset="./img/car4.webp" type="image/webp" />
                          <img src="./img/car4.jpg" alt="美食餐车" style="
                              width: 100%;
                              height: 200px;
                              object-fit: cover;
                            " />
                        </picture>
                      </div>
                      <div class="swiper-slide" style="width: 279.333px; margin-right: 28px">
                        <picture>
                          <source srcset="./img/car5.webp" type="image/webp" />
                          <img src="./img/car5.jpg" alt="美食餐车" style="
                              width: 100%;
                              height: 200px;
                              object-fit: cover;
                            " />
                        </picture>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </section>
        </div>
        <!-- 购房移民申请条件 -->
        <div class="application">
          <div class="width">
            <div class="conditions clearfix">
              <div class="pull-left conditions-left">
                <!-- <a href="/ympg.html" target="_blank"> -->
                <picture>
                  <source srcset="./img/America_Notice.webp" type="image/webp" />
                  <img src="./img/America_Notice.png" />
                </picture>
                <!-- </a> -->
              </div>
              <div class="pull-right conditions-right" style="padding-top: 0">
                <h3>美国EB-5为什么选择新辉煌？<br />&nbsp;</h3>
                <h4>
                  可提供EB-5直投全面服务方案，满足您的个性化需求；<br />&nbsp;
                </h4>
                <h4>
                  可提供独家源头EB-5直投项目——加州五星美食餐车；<br />&nbsp;
                </h4>
                <h4>
                  可提供EB-5项目背调服务，为您的EB-5之路排雷解忧；<br />&nbsp;
                </h4>
                <h4>
                  25年EB-5会计师合作经验，专长解决<strong>"资金来源证明"</strong>。<br />&nbsp;
                </h4>
              </div>
            </div>
          </div>
        </div>

        <h2>
          &nbsp;<br />
          &nbsp;<br />
        </h2>
      </div>
    </div>

    <!-- ========== 底部栏区块 ========== -->
    <!-- 底部栏（Footer，动态引入） -->
    <div id="footer-placeholder"></div>
    <script>
      fetch('footer.html')
        .then(res => res.text())
        .then(html => { document.getElementById('footer-placeholder').innerHTML = html; });
    </script>
    <!-- Footer End -->
    <!-- ========== 底部栏区块 End ========== -->

    <!-- ========== 侧边悬浮导航区块 ========== -->
    <div id="custom-service-placeholder"></div>
    <script>
      fetch('components/custom-service.html')
        .then(res => res.text())
        .then(html => {
          document.getElementById('custom-service-placeholder').innerHTML = html;
        });
    </script>
    <!-- ========== 侧边悬浮导航区块 End ========== -->

    <!-- ========== 本地JS依赖区块 ========== -->
    <!-- 脚本引入 -->
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/counterup/counterup.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/swiper-bundle.min.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        var projectSwiper = new Swiper(".ke_xss", {
          slidesPerView: 3,
          slidesPerGroup: 1,
          spaceBetween: 30,
          loop: true,
          autoplay: {
            delay: 3000,
            disableOnInteraction: false,
          },
          pagination: {
            el: ".swiper-pagination",
            clickable: true,
          },
          navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          },
          breakpoints: {
            320: {
              slidesPerView: 1,
              spaceBetween: 10,
            },
            768: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
          },
        });
      });
    </script>
    <!-- ========== 本地JS依赖区块 End ========== -->
</body>

</html>
