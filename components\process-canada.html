<!-- PC端流程区块 -->
<div class="Application-process process-canada-pc">
  <div class="width">
    <h3>加拿大创新企业家移民 (SUV)申请流程</h3>
    <div class="process-content-new">
      <div class="process_list-new15">
        <div class="right_half_circle"></div>
        <div class="process_cicle process_cicle_0">
          <div class="step_and_title">
            <p class="step">STEP 1</p>
            <p class="title">签约</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_0"></div>
        <div class="process_cicle process_cicle_1">
          <div class="step_and_title">
            <p class="step">STEP 2</p>
            <p class="title">提出想法，并匹配孵化器</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_1"></div>
        <div class="process_cicle process_cicle_2">
          <div class="step_and_title">
            <p class="step">STEP 3</p>
            <p class="title">获得孵化器支持信</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_2"></div>
        <div class="process_cicle process_cicle_3">
          <div class="step_and_title">
            <p class="step">STEP 4</p>
            <p class="title">递交工签申请（可选）</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_3"></div>
        <div class="process_cicle process_cicle_4">
          <div class="step_and_title">
            <p class="step">STEP 5</p>
            <p class="title">递交工签申请（可选）</p>
            <div class="desc"></div>
          </div>
        </div>
        <div style="margin-bottom: 154px; clear: both; width: 100%"></div>
        <div class="process_dashed_line process_dashed_line_4"></div>
        <div class="process_cicle process_cicle_5" style="float: right">
          <div class="step_and_title">
            <p class="step">STEP 6</p>
            <p class="title">客户参加公司运作</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_5" style="float: right"></div>
        <div class="process_cicle process_cicle_6" style="float: right">
          <div class="step_and_title">
            <p class="step">STEP 7</p>
            <p class="title">客户参加公司运作</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_6" style="float: right"></div>
        <div class="process_cicle process_cicle_7" style="float: right">
          <div class="step_and_title">
            <p class="step">STEP 8</p>
            <p class="title">永居申请获批</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_7" style="float: right"></div>
        <div class="process_cicle process_cicle_8" style="float: right">
          <div class="step_and_title">
            <p class="step">STEP 9</p>
            <p class="title">永居申请获批</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_8" style="float: right"></div>
        <div class="process_cicle process_cicle_9" style="float: right">
          <div class="step_and_title">
            <p class="step">STEP 10</p>
            <p class="title">永居申请获批</p>
            <div class="desc"></div>
          </div>
        </div>
        <div class="process_dashed_line process_dashed_line_9 globe_gw_company_abroad" style="float: right"></div>
      </div>
    </div>
  </div>
</div>

<!-- 移动端流程区块 -->
<div class="Application-process process-canada-mobile">
  <div class="width">
    <h3>加拿大创新企业家移民 (SUV)申请流程</h3>
    <div class="process-mobile-list">
      <div class="process-mobile-step"><div class="step-circle">1</div><div class="step-content"><div class="step-title">签约</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">2</div><div class="step-content"><div class="step-title">提出想法，并匹配孵化器</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">3</div><div class="step-content"><div class="step-title">获得孵化器支持信</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">4</div><div class="step-content"><div class="step-title">递交工签申请（可选）</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">5</div><div class="step-content"><div class="step-title">递交工签申请（可选）</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">6</div><div class="step-content"><div class="step-title">客户参加公司运作</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">7</div><div class="step-content"><div class="step-title">客户参加公司运作</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">8</div><div class="step-content"><div class="step-title">永居申请获批</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">9</div><div class="step-content"><div class="step-title">永居申请获批</div></div></div>
      <div class="process-mobile-step"><div class="step-circle">10</div><div class="step-content"><div class="step-title">永居申请获批</div></div></div>
    </div>
  </div>
</div>

<style>
.process-canada-pc { display: block; }
.process-canada-mobile { display: none; }
@media (max-width: 991px) {
  .process-canada-pc { display: none !important; }
  .process-canada-mobile { display: block !important; }
}
.process-canada-mobile .process-mobile-list {
  display: flex;
  flex-direction: column;
  gap: 18px;
  margin-top: 12px;
}
.process-canada-mobile .process-mobile-step {
  display: flex;
  align-items: flex-start;
  background: #fff;
  border-radius: 14px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.07);
  padding: 16px 12px;
  gap: 12px;
}
.process-canada-mobile .step-circle {
  width: 36px;
  height: 36px;
  background: #003a66;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2em;
  font-weight: bold;
  flex-shrink: 0;
  margin-right: 8px;
}
.process-canada-mobile .step-content {
  flex: 1;
}
.process-canada-mobile .step-title {
  font-size: 1.08em;
  font-weight: 600;
  color: #003a66;
  margin-bottom: 2px;
}
</style>
