﻿<!-- ========== 新辉煌出国 新加坡移民 Immigration-SingaporeEP.html ========== -->
<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <!-- ========== 头部meta与样式引入区块 ========== -->
    <meta charset="utf-8" />
    <title>新辉煌出国</title>
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <meta content="" name="keywords" />
    <meta content="" name="description" />

    <!-- 兜底样式，移动端适配 -->
    <link href="css/responsive.css" rel="stylesheet" />
    <!-- 本地字体文件，已本地化 -->
    <link rel="stylesheet" href="css/fontawesome/all.css" />

    <!-- 本地图标字体，已本地化 -->
    <link rel="stylesheet" href="css/bootstrap-icons/bootstrap-icons.css" />

    <!-- 本地动画库、轮播库样式 -->
    <link href="lib/animate/animate.min.css" rel="stylesheet" />
    <link href="lib/owlcarousel/assets/owl.carousel.min.css" rel="stylesheet" />

    <!-- 本地Bootstrap样式 -->
    <link href="css/bootstrap.min.css" rel="stylesheet" />

    <!-- 站点主样式，结构化注释 -->
    <link href="css/style.css" rel="stylesheet" />


    <!-- 结构化区块样式 -->
    <link rel="stylesheet" href="./css/merged/reset1.css" />
    <link rel="stylesheet" href="./css/merged/common1.css" />
    <link rel="stylesheet" href="./css/merged/window1.css" />
    <link rel="stylesheet" href="./css/merged/advisers1.css" />
    <link rel="stylesheet" href="./css/merged/flag_window.css" />
    <link rel="stylesheet" href="./css/merged/flag.css" />
    <link rel="stylesheet" href="./css/merged/swiper.css" />
    <link rel="stylesheet" href="./css/merged/globe_subject.css" />
    <link href="css/css4/gg1.css" rel="stylesheet" />
    <link rel="stylesheet" href="./css/merged/public.css" />
    <link rel="stylesheet" href="./css/merged/index_new.css" />
    <link rel="stylesheet" href="./css/merged/index-modern.css" />
    <link rel="stylesheet" href="./css/merged/project_page_cq_new.css" />
    <link rel="stylesheet" href="./css/merged/jinrErweima.css" />
    <link rel="stylesheet" href="./css/merged/flag(1).css" />

    <link rel="shortcut icon" href="图片/新辉煌logo.png" />

    <style>
      /* 隐藏style2样式 */
      .style2 {
        display: none;
      }
      .hover:hover {
        color: var(--bs-secondary) !important;
      }
      /* Swiper导航按钮自定义样式 */
      .swiper-button-next,
      .swiper-button-prev {
        background-color: rgba(255, 255, 255, 0.7);
        padding: 20px;
        border-radius: 50%;
        color: #003a66;
      }
      .swiper-button-next:after,
      .swiper-button-prev:after {
        font-size: 16px;
        font-weight: bold;
      }
      .swiper-pagination-bullet {
        width: 10px;
        height: 10px;
        background: #003a66;
      }
      .Breadcrumbs{
        height:auto !important;
      }
    </style>
    <!-- ========== 头部meta与样式引入区块 End ========== -->
  </head>

  <body>
    <!-- ========== 顶部加载动画区块 ========== -->
    <div id="spinner" class="show bg-white position-fixed translate-middle w-100 vh-100 top-50 start-50 d-flex align-items-center justify-content-center">
      <div class="spinner-border text-secondary" style="width: 3rem; height: 3rem" role="status">
        <span class="sr-only">加载中...</span>
      </div>
    </div>
    <!-- 顶部加载动画区块结束 -->

    <!-- ========== 顶部栏区块 ========== -->
    <div class="container-fluid bg-primary px-5 d-none d-lg-block">
      <div class="row gx-0 align-items-center">
        <div class="col-lg-5 text-center text-lg-start mb-lg-0">
          <div class="d-flex"></div>
        </div>
        <div class="col-lg-3 row-cols-1 text-center mb-2 mb-lg-0">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
        <div class="col-lg-4 text-center text-lg-end">
          <div
            class="d-inline-flex align-items-center"
            style="height: 45px"
          ></div>
        </div>
      </div>
    </div>
    <!-- 顶部栏区块结束 -->

    <!-- ========== 导航栏区块 ========== -->
    <!-- 头图区块 -->
    <div class="container-fluid nav-bar p-0">
        <nav
          class="navbar navbar-expand-lg navbar-light bg-white px-4 px-lg-5 py-3 py-lg-0"
        >
          <a href="" class="navbar-brand p-0">
            <h1 class="display-5 text-secondary m-0">
              <picture>
                <source srcset="图片/logo.webp" type="image/webp" />
                <img src="图片/logo.png" class="img-fluid" alt="" style="max-height: 80px" />
              </picture>
            </h1>
            <!-- <img src="img/logo.png" alt="Logo"> -->
          </a>
          <button
            class="navbar-toggler"
            type="button"
            data-bs-toggle="collapse"
            data-bs-target="#navbarCollapse"
          >
            <span class="fa fa-bars"></span>
          </button>
          <div class="collapse navbar-collapse" id="navbarCollapse">
            <div class="navbar-nav ms-auto py-0">
              <a href="index.html" class="nav-item nav-link">首页</a>
              <a href="about.html" class="nav-item nav-link">关于我们</a>
              <div class="nav-item dropdown">
                <a href="Immigration.html" class="nav-link active" data-bs-toggle="dropdown" onclick="window.location.href='Immigration.html'">
                <span class="dropdown-toggle">移民项目</span>
              </a>
                <div class="dropdown-menu m-0">
                  <a href=" Immigration-USA.html" class="dropdown-item"
                    >美国</a
                  >
                  <a href="Immigration-Canada.html" class="dropdown-item"
                    >加拿大</a
                  >
                  <a href="Immigration-Ireland.html" class="dropdown-item"
                    >爱尔兰</a
                  >
                  <a href="Immigration-HongKong.html" class="dropdown-item"
                    >香港</a
                  >
                  <a href="Immigration-SingaporeEP.html" class="dropdown-item active"
                    >新加坡</a
                  >
                  <a href="Immigration-Grenada.html" class="dropdown-item"
                    >格林纳达</a
                  >
                </div>
              </div>
            </div>
              <div class="nav-item dropdown">
                <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#jiaoyu'"
                ><span class="dropdown-toggle">国际教育</span></a
              >
                <div class="dropdown-menu m-0">
                  <a href="InternationalEducation-StudyAbroadApplication.html" class="dropdown-item"
                    >留学申请</a
                  >
                  <a href="InternationalEducation-OnlineCourses.html" class="dropdown-item"
                    >线上课程</a
                  >
                  <a href="InternationalEducation-TopSchools.html" class="dropdown-item"
                    >名校直通车计划</a
                  >
                  <a href="InternationalEducation-LanguageCourses.html" class="dropdown-item"
                    >语言课程</a
                  >
                  <a href="InternationalEducation-StudyTour-SpecialGroups.html" class="dropdown-item"
                    >游学、特色团</a
                  >
                  <a href="InternationalEducation-InternationalSchools.html" class="dropdown-item"
                    >国际学校</a
                  >
                </div>
              </div>
              <div class="nav-item dropdown">
                <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#haiwai'"
                ><span class="dropdown-toggle">海外置业</span></a
              >
                <div class="dropdown-menu m-0">
                  <a href="OverseasProperty-HongKong.html" class="dropdown-item">香港</a>
                  <a href="OverseasProperty-Dubai.html" class="dropdown-item">迪拜</a>
                  <a href="OverseasProperty-UK.html" class="dropdown-item">英国</a>
                  <a href="OverseasProperty-Ireland.html" class="dropdown-item">爱尔兰</a>
                </div>
              </div>
              <div class="nav-item dropdown">
                <a href="#" class="nav-link" data-bs-toggle="dropdown" onclick="window.location.href='index.html#pingtai'"
                ><span class="dropdown-toggle">平台合作</span></a
              >
                <div class="dropdown-menu m-0">
                  <a href="PlatformCooperation-HeheAbroad.html" class="dropdown-item"
                    >合和法律平台</a
                  >
                  <a href="PlatformCooperation-EliteStudyAbroad.html" class="dropdown-item"
                    >精英留学平台</a
                  >
                  <a href="PlatformCooperation-NewGloryFinance.html" class="dropdown-item"
                    >新辉煌金融平台</a
                  >
                </div>
              </div>
              <a href="contact.html" class="nav-item nav-link">联系我们</a>
            </div>
          </div>
        </nav>
      </div>
    <!-- Modal Search End -->

    <!-- ========== 头图区块 ========== -->
    <!-- Header Start -->
    <div
      class="container-fluid bg-breadcrumb size-breadcrumb"
      style="
        background: linear-gradient(rgba(0, 58, 102, 0), rgba(0, 58, 102, 0)),
          url(./图片/新加坡EP头图.jpg);
        background-position: center center;
        background-repeat: no-repeat;
        background-attachment: initial;
        background-size: contain;
        padding: 500px 0 60px 0;
      "
    >
      <div class="container text-center py-5" style="max-width: 900px">
        <h3
          class="text-white display-3 mb-4 wow fadeInDown"
          data-wow-delay="0.1s"
        ></h3>

        <ol
          class="breadcrumb justify-content-center text-white mb-0 wow fadeInDown"
          data-wow-delay="0.3s"
        >
          <li class="breadcrumb-item">
            <a href="index.html" class="text-white"></a>
          </li>
        </ol>
      </div>
    </div>
    <!-- Header End -->
    <!-- ========== 头图区块 End ========== -->

    <!-- ========== 主体内容区块 ========== -->
    <!-- 头部 -->

    <div class="c_project">
      <div class="main">
        <div class="mainInfo">
          <h1 class="hidden_title" style="color: black; padding-top: 50px; padding-bottom: 20px">
            新辉煌新加坡创业EP
          </h1>

          <h1 class="hidden_title"
            style="
              background-color: rgba(0, 0, 0, 0.3);
              padding-top: 0px;
              padding-bottom: 0px;
              font-size: 0.1rem;
            "
          >
            &nbsp;&nbsp;
          </h1>

          <div class="Breadcrumbs">
            <div class="width">
              <h3>新加坡创业EP</h3>
              <div class="b-content clearfix" style="position: relative">
                <div class="pull-left b-left">
                  <p class="two">
                    以国内商业背景和高管经验为依托，来新加坡成立新公司，申请人以自雇的形式去到新加坡公司担任管理职位并申请新加坡自雇工签。
                    在新加坡创业后衔接移民的条件简单，灵活的申请及续签条件，持有高管工签满2年可申请新加坡永居身份，持有永居2年可申请新加坡入籍。EP人士可为配偶和子女申请家属准证、父母申请长期签证，实现短时间内一家三代移居新加坡。快则3-4个月获批登陆新加坡；是目前衔接新加坡永居主流的途径。
                  </p>

                  <div style="display: flex; flex-wrap: nowrap">
                    <a
                    href="contact.html#contact"
                      class="pmq text-right"
                      style="display: block; margin: 42px 100px 0 0"
                      >在线咨询</a
                    >
                  </div>
                </div>
                <div class="pull-left b-right gs_office_imgs">
                  <picture>
                    <source srcset="图片/新加坡1.webp" type="image/webp" />
                    <img src="./图片/新加坡1.jpg" style="display: block" />
                  </picture>

                  <!-- <img src="/static/index/images/project/b-right-video-btn.png" alt="" class="b-right-video-btn"> -->
                  <div class="b-right-items">
                    <div class="b-right-items-info">
                      <picture>
                        <source srcset="图片/新加坡1.webp" type="image/webp" />
                        <img
                        style="
                          width: 24px;
                          height: 21px;
                          border-radius: unset;
                          display: block;
                        "
                        data-original="css/css2/img/project_blzq.png"
                        src="./css/css2/img/project_blzq.png"
                        alt=""
                      />
                      </picture>
                      <p>办理周期</p>
                      <p>1-3个月</p>
                    </div>
                    <div class="b-right-items-info">
                      <picture>
                        <source srcset="图片/新加坡1.webp" type="image/webp" />
                        <img
                        style="
                          width: 34px;
                          height: 20px;
                          border-radius: unset;
                          display: block;
                        "
                        data-original="css/css2/img/project_hdsf.png"
                        src="./css/css2/img/project_hdsf.png"
                        alt=""
                      />
                      </picture>
                      <p>获得身份</p>
                      <p>高级工作准证</p>
                    </div>
                    <div class="b-right-items-info">
                      <picture>
                        <source srcset="图片/新加坡1.webp" type="image/webp" />
                        <img
                        style="
                          width: 24px;
                          height: 21px;
                          border-radius: unset;
                          display: block;
                        "
                        data-original="css/css2/img/project_zcyq.png"
                        src="./css/css2/img/project_zcyq.png"
                        alt=""
                      />
                      </picture>
                      <p>资产要求</p>
                      <p>约30万新币</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 购房移民申请条件 -->
          <div class="application">
            <div class="width">
              <div class="conditions clearfix">
                <div class="pull-left conditions-left">
                  <picture>
                    <source srcset="图片/新加坡2.webp" type="image/webp" />
                    <img src="./图片/新加坡2.jpg" />
                  </picture>
                </div>
                <div class="pull-right conditions-right">
                  <h3>新加坡创业EP申请条件</h3>
                  <p>
                    1、申请人需有创业或管理或投资经验<br />
                    2、准备30万新币用于未来薪酬发放和日常开销<br />
                    3、申请人满足新加坡EP20分基础打分<br />
                  </p>
                </div>
              </div>
            </div>
          </div>
          <!-- 申请流程和申请费用   香港 台湾文章排除
          <div class="Application-process">
            <div class="width">
              <h3>新加坡创业EP申请流程</h3>
              <div class="process-content-new">
                <div class="process_list-new15">
                  <div class="right_half_circle"></div>
                  <div class="process_cicle process_cicle_0">
                    <div class="step_and_title">
                      <p class="step">STEP 1</p>
                      <p class="title">签约</p>
                      <div class="desc">
                        签约，准备申请材料，咨询客户在港投资计划
                      </div>
                    </div>
                  </div>
                  <div class="process_dashed_line process_dashed_line_0"></div>
                  <div class="process_cicle process_cicle_1">
                    <div class="step_and_title">
                      <p class="step">STEP 2</p>
                      <p class="title">完成注册公司和开公户</p>
                      <div class="desc">
                        协助客户注册新加坡公司及开通新加坡公司账户
                      </div>
                    </div>
                  </div>
                  <div class="process_dashed_line process_dashed_line_1"></div>
                  <div class="process_cicle process_cicle_2">
                    <div class="step_and_title">
                      <p class="step">STEP 3</p>
                      <p class="title">完成租赁办公场所</p>
                      <div class="desc">公司注册后，协助租赁办公场所</div>
                    </div>
                  </div>
                  <div class="process_dashed_line process_dashed_line_2"></div>
                  <div class="process_cicle process_cicle_3">
                    <div class="step_and_title">
                      <p class="step">STEP 4</p>
                      <p class="title">递交申请</p>
                      <div class="desc">新加坡人力部审核及补料</div>
                    </div>
                  </div>
                  <div class="process_dashed_line process_dashed_line_3"></div>
                  <div class="process_cicle process_cicle_4">
                    <div class="step_and_title">
                      <p class="step">STEP 5</p>
                      <p class="title">获批</p>
                      <div class="desc">获得批复</div>
                    </div>
                  </div>
                  <div
                    style="margin-bottom: 154px; clear: both; width: 100%"
                  ></div>
                  <div class="process_dashed_line process_dashed_line_4"></div>
                  <div
                    class="process_cicle process_cicle_5"
                    style="float: right"
                  >
                    <div class="step_and_title">
                      <p class="step">STEP 6</p>
                      <p class="title">登陆办卡</p>
                      <div class="desc">陪同办理取卡手续</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div> -->
          <!-- 新加坡创业EP申请流程 组件化引入区块 -->
          <div id="process-singapore-container"></div>
          <script>
            fetch('components/process-singapore.html')
              .then(res => res.text())
              .then(html => {
                document.getElementById('process-singapore-container').innerHTML = html;
              });
          </script>
          <!-- 新加坡创业EP申请流程区块结束 -->

          <!-- 新加坡创业EP优势区块 -->
          <div class="application">
            <div class="width">
              <div class="conditions clearfix">
                <div class="pull-left conditions-left">
                  <!-- <a href="/ympg.html" target="_blank"> -->
                  <picture>
                    <source srcset="图片/新加坡2.webp" type="image/webp" />
                    <img src="./图片/新加坡2.jpg" />
                  </picture>
                  <!-- </a> -->
                </div>
                <div class="pull-right conditions-right" style="padding-top: 0">
                  <h3>新加坡创业EP为什么选择新辉煌？<br />&nbsp;</h3>
                  <h4>完善的产品线让您选择无忧<br />&nbsp;</h4>
                  <h4>项目开发团队让您放心无忧<br />&nbsp;</h4>
                  <h4>线上化服务让您沟通无忧<br />&nbsp;</h4>
                  <h4>良好政府关系让您递交无忧<br />&nbsp;</h4>
                </div>
              </div>
            </div>
          </div>
          <!-- 新加坡创业EP优势区块结束 -->
        </div>
      </div>
    </div>
    <!-- ========== 主体内容区块 End ========== -->

    <!-- ========== 底部栏区块 ========== -->
    <!-- 底部栏组件化引入区块 -->
          <div id="footer-placeholder"></div>
          <script>
            fetch('footer.html')
              .then(res => res.text())
              .then(html => { document.getElementById('footer-placeholder').innerHTML = html; });
          </script>
          <!-- 底部栏区块结束 -->
    <!-- ========== 底部栏区块 End ========== -->

    <!-- ========== 侧边悬浮导航区块 ========== -->
          <div id="custom-service-placeholder"></div>
          <script>
            fetch('components/custom-service.html')
              .then(res => res.text())
              .then(html => {
                document.getElementById('custom-service-placeholder').innerHTML = html;
              });
          </script>
          <!-- ========== 侧边悬浮导航区块 End ========== -->

    <!-- ========== 本地JS依赖区块 ========== -->
    <!-- 本地JS库依赖，全部本地化 -->
    <script src="js/jquery-3.6.4.min.js"></script>
    <script src="js/bootstrap.bundle.min.js"></script>
    <script src="lib/wow/wow.min.js"></script>
    <script src="lib/easing/easing.min.js"></script>
    <script src="lib/waypoints/waypoints.min.js"></script>
    <script src="lib/counterup/counterup.min.js"></script>
    <script src="lib/owlcarousel/owl.carousel.min.js"></script>
    <script src="js/main.js"></script>
    <script src="js/swiper-bundle.min.js"></script>
    <!-- Swiper初始化代码 -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        var projectSwiper = new Swiper(".ke_xss", {
          slidesPerView: 3,
          slidesPerGroup: 1,
          spaceBetween: 30,
          loop: true,
          autoplay: {
            delay: 3000,
            disableOnInteraction: false,
          },
          pagination: {
            el: ".swiper-pagination",
            clickable: true,
          },
          navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          },
          breakpoints: {
            320: {
              slidesPerView: 1,
              spaceBetween: 10,
            },
            768: {
              slidesPerView: 2,
              spaceBetween: 20,
            },
            1024: {
              slidesPerView: 3,
              spaceBetween: 30,
            },
          },
        });
      });
    </script>
    <!-- ========== 本地JS依赖区块 End ========== -->
  </body>
</html>

