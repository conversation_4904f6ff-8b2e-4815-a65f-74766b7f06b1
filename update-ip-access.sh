#!/bin/bash

# IP地址更新脚本
# 当您的IP地址变化时使用此脚本更新防火墙规则

if [ "$EUID" -ne 0 ]; then
    echo "请使用sudo运行此脚本"
    exit 1
fi

if [ -z "$1" ] || [ -z "$2" ]; then
    echo "使用方法: sudo bash $0 OLD_IP NEW_IP"
    echo "例如: sudo bash $0 ************* *************"
    exit 1
fi

OLD_IP=$1
NEW_IP=$2
SSH_PORT=2222

echo "更新IP访问权限..."
echo "旧IP: $OLD_IP"
echo "新IP: $NEW_IP"

# 更新UFW规则
echo "更新防火墙规则..."
ufw delete allow from $OLD_IP to any port $SSH_PORT
ufw allow from $NEW_IP to any port $SSH_PORT

# 更新Fail2Ban配置
echo "更新Fail2Ban配置..."
sed -i "s/$OLD_IP/$NEW_IP/g" /etc/fail2ban/jail.local
systemctl restart fail2ban

# 更新hosts.allow（如果使用）
if [ -f /etc/hosts.allow ]; then
    sed -i "s/$OLD_IP/$NEW_IP/g" /etc/hosts.allow
fi

echo "IP更新完成！"
echo "新的访问IP: $NEW_IP"
echo "SSH连接命令: ssh -p $SSH_PORT username@服务器IP"
