

/*
 * nav style698
 */
.nav {
	width: 100% ;
	height: 48px ;
	background: rgba(222,204,180,1) ;
	box-shadow:0 0 2px 5px rgba(0,0,0,.3);
	z-index:4;
}
.nav-in {
	width:1030px;
	height:100%;
	background: url(/statics/images/init_new/nav_bj.jpg) repeat-y center top;
	margin: 0 auto; 
}
.nav-in>li {
    font-size: 16px;
    height: 48px;
    line-height: 48px;
	width: 85px;

}
.nav-in>li>a:hover {
	color: rgba(144,0,0,1);
}
.nav-in>li .yuan{display: block;width: 6px;height: 6px;border-radius: 5px;position: absolute;top: 13px;left: 8px;background-color: #f00;}
.gm-project> a  {
	text-indent: 13px ;
	text-align: left ;
	background: url(/statics/images/icon-xia.png) no-repeat 85px center ;
}
.gm-project .s{url(/statics/images/newindex/3_01.png) no-repeat 85px center ;color:#000}
.about-cur {
	color: rgba(144,0,0,1);
	border-left: 1px solid rgba(190,157,103,1) ;
	border-right: 1px solid rgba(190,157,103,1) ;
	background: rgba(249,239,229,1) url(/statics/images/icon-shang.png) no-repeat 85px center!important;
}
.gmp-ul  {
	width: 750px ;
	border: 1px solid rgba(190,157,103,1);
	border-top: none ;
	background: rgba(249,239,229,1) ;
	display:none;
}
.gmp-ul > li > a {
	width:102px;
	font-size: 14px ;
	text-indent: 20px ;
	text-align: left ;
	background: url(/statics/images/icon-black_jiantou.png) no-repeat 85px center ;
	border-right: 1px solid rgba(190,157,103,1) ;
	border-bottom: 1px dashed rgba(190,157,103,1);
}
.gmp-ul > li:last-child > a {
	border-bottom: none ;
}
.gmp-cur {
	color: rgba(144,0,0,1);
	border-right: none!important ;
	background: rgba(249,239,229,1) url(/statics/images/icon-jiantou.png) no-repeat 85px center!important;
}
.nav-in>li .about-cur-s {
	color: #900;
	border-left: 1px solid rgba(190,157,103,1) ;
	border-right: 1px solid rgba(190,157,103,1) ;
	background: rgba(249,239,229,1) url(/statics/images/newindex/3_02.png) no-repeat 85px center!important;
}
.gmp-list > ul {
	width:600px;
	height: 269px;
	left:125px;
	top:0;
	display: none ;
}
.gmp-list:first-child > ul  {
	display: block ;
}

.gmp-list > ul  > li {
	width:180px;
	height:43px;
	margin:5px 10px 0;
}
.gmp-list > ul  > li > span , .gmp-list > ul  > li > p {
	line-height: 24px ;
	font-size: 14px;
}
.gmp-list > ul  > li > span:hover , .gmp-list > ul  > li   a:hover {
	color: rgba(144,0,0,1);
}
.listf li {
	width:88px!important;
}
.sb a {
	background:none!important;
}

.more-a{width: 50px; float: right;color: #900000;}
